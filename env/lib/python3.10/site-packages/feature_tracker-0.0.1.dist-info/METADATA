Metadata-Version: 2.4
Name: feature_tracker
Version: 0.0.1
Summary: An internal feature tracking tool built with Frappe, designed to help teams monitor the development progress of product features, assign responsibilities, and track deadlines and status updates — all in one place.
Author-email: <PERSON> <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/markdown

### Feature Tracker

An internal feature tracking tool built with Frappe, designed to help teams monitor the development progress of product features, assign responsibilities, and track deadlines and status updates — all in one place.

### Installation

You can install this app using the [bench](https://github.com/frappe/bench) CLI:

```bash
cd $PATH_TO_YOUR_BENCH
bench get-app $URL_OF_THIS_REPO --branch develop
bench install-app feature_tracker
```

### Contributing

This app uses `pre-commit` for code formatting and linting. Please [install pre-commit](https://pre-commit.com/#installation) and enable it for this repository:

```bash
cd apps/feature_tracker
pre-commit install
```

Pre-commit is configured to use the following tools for checking and formatting your code:

- ruff
- eslint
- prettier
- pyupgrade

### License

mit

