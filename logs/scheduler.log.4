2025-06-25 11:25:45,787 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-06-25 11:25:45,792 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for nexus.com
2025-06-25 11:25:45,806 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for nexus.com
2025-06-25 11:25:45,811 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for nexus.com
2025-06-25 11:25:45,817 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for nexus.com
2025-06-25 11:25:45,822 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for nexus.com
2025-06-25 11:25:45,827 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for nexus.com
2025-06-25 11:25:45,943 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for nexus.com
2025-06-25 11:25:45,946 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for nexus.com
2025-06-25 11:25:45,949 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for nexus.com
2025-06-25 11:25:45,952 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for nexus.com
2025-06-25 11:25:46,045 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for nexus.com
2025-07-05 16:03:32,326 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-07-05 16:03:32,329 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-07-05 16:03:32,333 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-07-05 16:03:32,335 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-07-05 16:03:32,340 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-07-05 16:03:32,354 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-07-05 16:03:32,356 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-07-05 16:03:32,359 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-07-05 16:03:32,361 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-07-05 16:03:32,366 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-07-05 16:03:32,372 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-07-05 16:03:32,378 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-07-05 16:03:32,383 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-07-05 16:03:32,393 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-07-05 16:03:32,397 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-07-05 16:03:32,399 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-07-05 16:03:32,401 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-07-05 16:03:32,404 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-07-05 16:03:32,406 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-07-05 16:03:32,408 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-07-05 16:03:32,413 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-07-05 16:03:32,422 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-07-05 16:03:32,423 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-07-05 16:03:32,425 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-07-05 16:03:32,433 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-07-05 16:03:32,436 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-07-05 16:03:32,439 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-07-05 16:03:32,440 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-07-05 16:03:32,455 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,460 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,462 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,463 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,466 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,468 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,473 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,476 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,479 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,483 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,485 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,488 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,493 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,499 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,503 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,506 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,510 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,515 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,522 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,524 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for brainwise.helpdesk
2025-07-05 16:03:32,766 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for nexus.com
2025-07-05 16:03:32,773 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for nexus.com
2025-07-05 16:03:32,780 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for nexus.com
2025-07-05 16:03:32,788 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for nexus.com
2025-07-05 16:03:32,792 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for nexus.com
2025-07-05 16:03:32,819 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for nexus.com
2025-07-05 16:03:32,830 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for nexus.com
2025-07-05 16:03:32,832 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for nexus.com
2025-07-05 16:03:32,839 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for nexus.com
2025-07-05 16:03:32,855 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for nexus.com
2025-07-05 16:03:32,859 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for nexus.com
2025-07-05 16:03:32,863 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-07-05 16:03:32,865 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for nexus.com
2025-07-05 16:03:32,871 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for nexus.com
2025-07-05 16:03:32,881 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for nexus.com
2025-07-05 16:03:32,884 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for nexus.com
2025-07-06 12:18:12,789 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-07-06 12:18:12,917 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-07-06 12:18:13,072 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for testmahmoud
2025-07-06 12:18:13,111 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for testmahmoud
2025-07-06 12:18:13,198 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for testmahmoud
2025-07-06 12:18:13,679 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for customhr
2025-07-06 12:18:13,751 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for customhr
2025-07-06 12:18:14,213 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for theme.com
2025-07-06 12:18:14,242 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for theme.com
2025-07-06 12:18:14,540 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for parentngo
2025-07-06 12:18:14,740 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for parentngo
2025-07-06 12:18:14,797 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for parentngo
2025-07-06 12:18:14,814 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-07-06 12:18:14,910 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for serviceplanner
2025-07-06 12:18:14,962 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for serviceplanner
2025-07-06 12:18:15,007 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for serviceplanner
2025-07-06 12:18:15,140 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-07-06 12:18:15,197 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-07-06 12:18:15,217 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-07-06 12:18:15,230 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-07-07 10:59:09,005 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-07-07 10:59:09,010 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-07-07 10:59:09,037 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-07-07 10:59:09,057 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-07-07 10:59:09,063 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-07-07 10:59:09,066 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-07-07 10:59:09,099 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-07-07 10:59:09,105 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-07-07 10:59:09,737 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for theme.com
2025-07-07 10:59:09,778 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for theme.com
2025-07-07 10:59:09,792 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for theme.com
2025-07-07 10:59:09,794 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for theme.com
2025-07-07 10:59:09,799 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for theme.com
2025-07-07 10:59:09,804 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for theme.com
2025-07-07 10:59:09,815 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-07-07 10:59:09,819 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for theme.com
2025-07-07 10:59:09,828 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for theme.com
2025-07-07 10:59:09,850 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-07-07 10:59:09,856 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for theme.com
2025-07-07 10:59:09,863 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for theme.com
2025-07-08 11:21:08,764 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for customhr
2025-07-08 11:21:08,787 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for customhr
2025-07-08 11:21:08,790 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for customhr
2025-07-08 11:21:08,815 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for customhr
2025-07-08 11:21:08,849 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for customhr
2025-07-08 11:21:08,906 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for customhr
2025-07-08 11:21:08,907 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for customhr
2025-07-08 11:21:08,917 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for customhr
2025-07-08 11:21:08,927 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for customhr
2025-07-08 11:21:08,929 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for customhr
2025-07-08 11:21:08,984 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for customhr
2025-07-08 11:21:09,020 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for customhr
2025-07-08 11:21:09,583 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-07-08 11:21:09,588 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-08 11:21:09,592 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-08 11:21:09,617 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-08 11:21:09,619 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-07-08 11:21:09,632 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-08 11:21:09,638 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-08 11:21:09,702 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-08 11:21:09,722 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-08 11:21:09,735 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-08 11:21:09,738 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-08 11:21:09,742 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-08 11:21:09,762 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-07-08 11:21:09,770 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-08 11:21:09,788 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-08 11:21:09,799 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-07-08 11:21:09,804 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-08 11:21:09,807 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-08 11:21:09,810 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-08 11:21:09,817 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-08 11:21:09,827 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-07-08 11:21:09,830 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-08 11:21:09,833 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-07-08 11:21:09,838 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-07-08 11:21:10,090 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-08 11:21:10,547 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-08 11:21:10,620 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-08 11:21:10,867 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for nexus.com
2025-07-08 11:21:10,968 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for nexus.com
2025-07-08 11:21:10,984 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for nexus.com
2025-07-08 11:21:10,992 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for nexus.com
2025-07-08 11:21:11,110 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for nexus.com
2025-07-08 11:21:11,160 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for nexus.com
2025-07-08 11:21:11,236 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for nexus.com
2025-07-08 11:21:11,281 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for nexus.com
2025-07-08 11:21:11,283 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for nexus.com
2025-07-08 11:21:11,288 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for nexus.com
2025-07-08 11:21:11,296 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for nexus.com
2025-07-08 11:21:11,306 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for nexus.com
2025-07-08 11:21:11,330 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for nexus.com
2025-07-08 11:21:11,380 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-07-08 11:21:11,498 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for nexus.com
2025-07-08 11:21:11,526 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for nexus.com
2025-07-08 11:21:11,551 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for nexus.com
2025-07-08 11:21:11,636 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for nexus.com
2025-07-08 11:21:11,745 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-07-08 11:21:11,806 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-07-08 11:21:11,819 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-08 11:21:11,844 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-07-08 11:21:11,879 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-07-08 11:21:12,045 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-07-08 11:21:12,080 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-07-08 11:21:12,228 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-07-08 11:21:12,440 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-07-08 11:21:12,572 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-07-08 11:21:12,607 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-07-08 11:21:12,624 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-07-08 11:21:12,633 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-07-08 11:21:12,657 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-07-08 11:21:12,673 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-07-08 11:21:12,689 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-07-08 11:21:12,725 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-07-08 11:21:12,752 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-07-08 11:21:12,754 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-07-08 11:21:12,759 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-07-08 11:21:12,779 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-07-08 11:21:12,841 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-07-08 11:21:12,878 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-07-08 11:21:12,961 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-07-08 11:21:12,963 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-08 11:21:12,970 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-07-08 11:21:12,973 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-07-08 11:21:14,097 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,158 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,186 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,223 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,310 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,367 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,381 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,385 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,390 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,397 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,399 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,412 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,445 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,447 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,451 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,500 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,549 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,551 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,553 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,557 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-07-08 11:21:14,725 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-07-08 11:21:14,733 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-07-08 11:21:14,782 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-07-08 11:21:14,809 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-07-08 11:21:14,825 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-07-08 11:21:14,827 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-07-08 11:21:14,837 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-07-08 11:21:14,843 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-07-08 11:21:14,847 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-07-08 11:21:14,849 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-07-08 11:21:14,886 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-07-08 11:21:14,953 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-07-08 11:21:14,955 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-07-08 11:21:14,956 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-08 11:21:14,966 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-08 11:21:15,029 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-07-08 11:21:15,054 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-07-08 11:21:15,056 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-07-08 11:21:15,057 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-07-08 11:21:15,060 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-07-08 11:21:15,068 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-07-08 11:21:15,078 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-07-08 11:21:15,081 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-07-08 11:21:15,082 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-08 11:21:15,085 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-07-08 11:21:15,086 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-07-08 11:21:15,089 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-07-08 11:21:15,091 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-07-13 11:18:50,749 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-13 11:18:50,752 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-07-13 11:18:50,761 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-07-13 11:18:50,764 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-13 11:18:50,769 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for serviceplanner
2025-07-13 11:18:50,775 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-13 11:18:50,781 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-13 11:18:50,795 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-13 11:18:50,801 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for serviceplanner
2025-07-13 11:18:50,805 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for serviceplanner
2025-07-13 11:18:50,813 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-07-13 11:18:50,816 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-07-13 11:18:50,820 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-13 11:18:50,826 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-13 11:18:50,831 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-07-13 11:18:50,851 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-07-13 11:18:50,860 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-13 11:18:50,869 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-13 11:18:50,872 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-13 11:18:50,874 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-13 11:18:50,878 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-13 11:18:50,901 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-07-13 11:18:50,907 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for serviceplanner
2025-07-13 11:18:50,913 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-13 11:18:50,916 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for serviceplanner
2025-07-13 11:18:50,925 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-13 11:18:50,933 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-13 11:18:50,936 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-13 11:18:50,942 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-13 11:18:50,955 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-13 11:18:50,961 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-07-13 11:18:50,968 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-13 11:18:50,971 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-07-13 11:18:50,974 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-13 11:18:50,994 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:50,997 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,002 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,006 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,009 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,012 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,015 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,018 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,022 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,025 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,030 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,033 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,037 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,041 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,045 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,049 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,052 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,056 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,063 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,066 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,070 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,074 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,078 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,081 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,087 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,091 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,094 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,098 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,101 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,105 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,109 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,112 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,118 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,122 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,125 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,127 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,131 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,134 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,137 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,141 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,144 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,147 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,151 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for brainwise.helpdesk
2025-07-13 11:18:51,190 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for customhr
2025-07-13 11:18:51,198 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for customhr
2025-07-13 11:18:51,221 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for customhr
2025-07-13 11:18:51,234 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for customhr
2025-07-13 11:18:51,247 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for customhr
2025-07-13 11:18:51,295 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for customhr
2025-07-13 11:18:51,363 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for customhr
2025-07-13 11:18:51,376 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for customhr
2025-07-13 11:18:51,379 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for customhr
2025-07-13 11:18:51,382 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for customhr
2025-07-13 11:18:51,388 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for customhr
2025-07-13 11:18:51,398 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for customhr
2025-07-13 11:18:51,400 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for customhr
2025-07-13 11:18:51,408 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for customhr
2025-07-13 11:18:51,412 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for customhr
2025-07-13 11:18:51,419 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for customhr
2025-07-13 11:18:51,422 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for customhr
2025-07-13 11:18:51,425 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for customhr
2025-07-13 11:18:51,438 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for customhr
2025-07-13 11:18:51,478 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for testmahmoud
2025-07-13 11:18:51,483 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-07-13 11:18:51,488 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-07-13 11:18:51,491 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-07-13 11:18:51,500 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-07-13 11:18:51,506 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for testmahmoud
2025-07-13 11:18:51,525 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-07-13 11:18:51,532 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-07-13 11:18:51,541 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-07-13 11:18:51,548 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for testmahmoud
2025-07-13 11:18:51,564 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-07-13 11:18:51,566 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-07-13 11:18:51,570 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-07-13 11:18:51,573 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-07-13 11:18:51,576 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for testmahmoud
2025-07-13 11:18:51,583 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-07-13 11:18:51,589 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for testmahmoud
2025-07-13 11:18:51,599 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-13 11:18:51,602 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-07-13 11:18:51,605 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for testmahmoud
2025-07-13 11:18:51,610 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-07-13 11:18:51,613 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-07-13 11:18:51,626 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-07-13 11:18:51,631 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-07-13 11:18:51,634 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-07-13 11:18:51,640 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-13 11:18:51,643 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-07-13 11:18:51,651 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-07-13 11:18:51,658 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for testmahmoud
2025-07-13 11:18:51,661 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-07-13 11:18:51,664 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-07-13 11:18:51,668 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-07-13 11:18:51,673 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-07-13 11:18:51,676 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-07-13 11:18:51,722 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for nexus.com
2025-07-13 11:18:51,738 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for nexus.com
2025-07-13 11:18:51,746 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for nexus.com
2025-07-13 11:18:51,751 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for nexus.com
2025-07-13 11:18:51,755 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for nexus.com
2025-07-13 11:18:51,763 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for nexus.com
2025-07-13 11:18:51,778 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for nexus.com
2025-07-13 11:18:51,793 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for nexus.com
2025-07-13 11:18:51,797 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for nexus.com
2025-07-13 11:18:51,805 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for nexus.com
2025-07-13 11:18:51,808 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for nexus.com
2025-07-13 11:18:51,812 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for nexus.com
2025-07-13 11:18:51,821 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for nexus.com
2025-07-13 11:18:51,832 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for nexus.com
2025-07-13 11:18:51,841 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for nexus.com
2025-07-13 11:18:51,845 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for nexus.com
2025-07-13 11:18:51,849 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-07-13 11:18:51,852 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for nexus.com
2025-07-13 11:18:51,856 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for nexus.com
2025-07-13 11:18:51,860 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for nexus.com
2025-07-13 11:18:51,863 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for nexus.com
2025-07-13 11:18:51,866 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for nexus.com
2025-07-13 11:18:51,869 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for nexus.com
2025-07-13 11:18:51,874 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for nexus.com
2025-07-13 11:18:51,877 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for nexus.com
2025-07-13 11:18:51,881 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for nexus.com
2025-07-13 11:18:51,892 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for nexus.com
2025-07-13 11:18:51,902 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for nexus.com
2025-07-13 11:18:51,906 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for nexus.com
2025-07-13 11:18:51,920 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for nexus.com
2025-07-13 11:18:51,923 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for nexus.com
2025-07-13 11:18:51,928 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for nexus.com
2025-07-13 11:18:51,934 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for nexus.com
2025-07-13 11:18:51,945 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for nexus.com
2025-07-13 11:18:51,965 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-07-13 11:18:51,968 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-07-13 11:18:51,973 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-07-13 11:18:51,975 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-07-13 11:18:51,979 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-07-13 11:18:51,984 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-07-13 11:18:51,989 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-07-13 11:18:52,000 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-07-13 11:18:52,009 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-07-13 11:18:52,013 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for parentngo
2025-07-13 11:18:52,016 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-07-13 11:18:52,021 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-07-13 11:18:52,029 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for parentngo
2025-07-13 11:18:52,048 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-07-13 11:18:52,058 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-07-13 11:18:52,061 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-07-13 11:18:52,066 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-07-13 11:18:52,069 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-07-13 11:18:52,075 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for parentngo
2025-07-13 11:18:52,082 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-07-13 11:18:52,085 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-07-13 11:18:52,090 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for parentngo
2025-07-13 11:18:52,093 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-07-13 11:18:52,098 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-07-13 11:18:52,102 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-07-13 11:18:52,110 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-07-13 11:18:52,115 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for parentngo
2025-07-13 11:18:52,118 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for parentngo
2025-07-13 11:18:52,126 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-07-13 11:18:52,138 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for parentngo
2025-07-13 11:18:52,143 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-07-13 11:18:52,157 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-07-13 11:18:52,160 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-07-13 11:18:52,169 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-07-13 11:18:52,177 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-07-13 11:18:52,201 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for theme.com
2025-07-13 11:18:52,203 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-07-13 11:18:52,212 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for theme.com
2025-07-13 11:18:52,225 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for theme.com
2025-07-13 11:18:52,245 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for theme.com
2025-07-13 11:18:52,248 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for theme.com
2025-07-13 11:18:52,253 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for theme.com
2025-07-13 11:18:52,256 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for theme.com
2025-07-13 11:18:52,284 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-07-13 11:18:52,297 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for theme.com
2025-07-13 11:18:52,321 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for theme.com
2025-07-13 11:18:52,339 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for theme.com
2025-07-13 11:18:52,348 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for theme.com
2025-07-13 11:18:52,351 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for theme.com
2025-07-13 11:18:52,364 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for theme.com
2025-07-13 11:18:52,367 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for theme.com
2025-07-13 11:18:52,370 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for theme.com
2025-07-13 11:18:52,375 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for theme.com
2025-07-13 11:18:52,391 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for theme.com
2025-07-13 11:18:52,419 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-07-13 11:18:52,423 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-07-13 11:18:52,425 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-07-13 11:18:52,439 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-07-13 11:18:52,522 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-07-13 11:18:52,529 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for child_ngo
2025-07-13 11:18:52,533 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for child_ngo
2025-07-13 11:18:52,561 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-07-13 11:18:52,568 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-07-13 11:18:52,571 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-07-13 11:18:52,573 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for child_ngo
2025-07-13 11:18:52,587 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for child_ngo
2025-07-13 11:18:52,593 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for child_ngo
2025-07-13 11:18:52,596 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-07-13 11:18:52,604 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for child_ngo
2025-07-13 11:18:52,606 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-07-13 11:18:52,625 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for child_ngo
2025-07-13 11:18:52,633 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-07-13 11:18:52,635 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-07-13 11:18:52,641 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-07-13 11:18:52,651 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-07-13 11:18:52,656 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-13 11:18:52,676 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-07-13 11:18:52,678 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-07-13 11:18:52,681 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-13 11:18:52,684 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-07-13 11:18:52,690 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-07-13 11:18:52,693 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-07-13 11:18:52,696 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-07-13 11:18:52,704 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-07-13 11:18:52,708 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-07-13 11:18:52,711 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-13 11:18:52,715 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-07-13 11:18:52,724 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-07-13 11:18:52,732 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-07-14 11:45:05,832 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-07-14 11:45:05,853 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-07-14 11:45:05,884 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-07-14 11:45:05,906 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-07-14 11:45:05,952 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-07-15 10:51:51,857 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-07-15 10:51:51,863 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-07-15 10:51:51,875 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-07-15 10:51:51,878 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-07-15 10:51:51,891 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-07-15 10:51:51,896 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-07-15 10:51:51,900 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-07-15 10:51:51,902 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-07-15 10:51:51,908 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-15 10:51:51,912 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-07-15 10:51:51,915 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-07-15 10:51:51,928 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-07-15 10:51:51,935 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-07-15 10:51:51,939 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-07-15 10:51:51,942 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-07-15 10:51:51,944 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-07-15 10:51:51,952 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-07-15 10:51:51,955 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-07-15 10:51:51,962 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-07-15 10:51:51,969 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-07-15 10:51:51,972 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-07-15 10:51:51,986 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-07-15 10:51:51,997 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-07-15 10:51:52,000 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-15 10:51:52,003 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-07-15 10:51:52,008 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-07-15 10:51:52,010 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-07-15 10:51:52,035 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for customhr
2025-07-15 10:51:52,039 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for customhr
2025-07-15 10:51:52,056 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for customhr
2025-07-15 10:51:52,062 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for customhr
2025-07-15 10:51:52,072 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for customhr
2025-07-15 10:51:52,143 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for customhr
2025-07-15 10:51:52,149 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for customhr
2025-07-15 10:51:52,161 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for customhr
2025-07-15 10:51:52,167 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for customhr
2025-07-15 10:51:52,172 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for customhr
2025-07-15 10:51:52,180 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for customhr
2025-07-15 10:51:52,187 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for customhr
2025-07-15 10:51:52,204 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-15 10:51:52,212 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-15 10:51:52,214 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-15 10:51:52,216 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-15 10:51:52,229 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-15 10:51:52,231 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-15 10:51:52,233 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-15 10:51:52,235 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-07-15 10:51:52,237 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-15 10:51:52,239 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-15 10:51:52,247 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-15 10:51:52,253 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-15 10:51:52,255 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-15 10:51:52,258 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-15 10:51:52,266 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-15 10:51:52,271 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-15 10:51:52,273 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-07-15 10:51:52,287 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-15 10:51:52,289 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-07-15 10:51:52,301 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-15 10:51:52,304 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-07-15 10:51:52,307 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-07-15 10:51:52,314 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-15 10:51:52,318 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-15 10:51:52,325 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-07-15 10:51:52,334 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-07-15 10:51:52,338 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-15 10:51:52,617 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-07-17 17:13:29,129 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-17 17:13:29,136 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-17 17:13:29,139 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-17 17:13:29,143 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-17 17:13:29,151 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-17 17:13:29,153 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-17 17:13:29,164 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-17 17:13:29,169 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-17 17:13:29,172 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-17 17:13:29,174 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-17 17:13:29,177 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-17 17:13:29,189 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-17 17:13:29,232 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-17 17:13:29,247 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-17 17:13:29,251 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-17 17:13:29,255 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-20 11:56:09,982 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for customhr
2025-07-20 11:56:09,988 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for customhr
