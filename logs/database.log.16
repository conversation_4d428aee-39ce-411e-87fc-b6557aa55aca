2025-06-26 10:13:46,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-06-26 10:13:46,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry` MODIFY `transaction_exchange_rate` decimal(21,9) not null default 0, MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0, MODIFY `credit_in_transaction_currency` decimal(21,9) not null default 0, MODIFY `debit_in_transaction_currency` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0
2025-06-26 10:13:46,797 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry` ADD INDEX `creation`(`creation`)
2025-06-26 10:13:47,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `print_receipt_on_order_complete` int(1) not null default 0, ADD COLUMN `disable_grand_total_to_default_mop` int(1) not null default 0, ADD COLUMN `allow_partial_payment` int(1) not null default 0, ADD COLUMN `project` varchar(140)
2025-06-26 10:13:47,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-06-26 10:13:47,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-26 10:13:47,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-06-26 10:13:48,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `pos_invoice` varchar(140), ADD COLUMN `pos_invoice_item` varchar(140)
2025-06-26 10:13:48,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-26 10:13:48,273 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `pos_invoice_index`(`pos_invoice`), ADD INDEX `creation`(`creation`)
2025-06-26 10:13:48,369 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withheld Vouchers` MODIFY `taxable_amount` decimal(21,9) not null default 0
2025-06-26 10:13:48,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD COLUMN `base_outstanding` decimal(21,9) not null default 0, ADD COLUMN `base_paid_amount` decimal(21,9) not null default 0
2025-06-26 10:13:48,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` MODIFY `invoice_portion` decimal(21,9) not null default 0, MODIFY `outstanding` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `discount` decimal(21,9) not null default 0, MODIFY `base_payment_amount` decimal(21,9) not null default 0, MODIFY `payment_amount` decimal(21,9) not null default 0
2025-06-26 10:13:48,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD INDEX `creation`(`creation`)
2025-06-26 10:13:49,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0
2025-06-26 10:13:49,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD INDEX `project_index`(`project`)
2025-06-26 10:13:49,495 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` ADD COLUMN `dont_enforce_free_item_qty` int(1) not null default 0
2025-06-26 10:13:49,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` MODIFY `threshold_percentage` decimal(21,9) not null default 0, MODIFY `max_qty` decimal(21,9) not null default 0, MODIFY `min_qty` decimal(21,9) not null default 0, MODIFY `recurse_for` decimal(21,9) not null default 0, MODIFY `free_item_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0
2025-06-26 10:13:49,975 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-26 10:13:49,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-26 10:13:50,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` ADD COLUMN `categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)'
2025-06-26 10:13:50,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-06-26 10:13:50,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-06-26 10:13:50,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` ADD INDEX `creation`(`creation`)
2025-06-26 10:13:51,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-06-26 10:13:51,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-26 10:13:51,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` ADD COLUMN `subcontracted_quantity` decimal(21,9) not null default 0, ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-26 10:13:51,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-26 10:13:52,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0, ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-26 10:13:52,209 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-06-26 10:13:52,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-26 10:13:52,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-26 10:13:52,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-26 10:13:52,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-06-26 10:13:52,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject User` ADD COLUMN `hide_timesheets` int(1) not null default 0
2025-06-26 10:13:53,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-26 10:13:53,485 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-26 10:13:53,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD INDEX `project_index`(`project`)
2025-06-26 10:13:54,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-26 10:13:54,055 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-26 10:13:54,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-26 10:13:54,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-06-26 10:13:54,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `cost_center` varchar(140), ADD COLUMN `project` varchar(140)
2025-06-26 10:13:54,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `production_plan_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `produced_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `work_order_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-26 10:13:54,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD INDEX `project_index`(`project`)
2025-06-26 10:13:55,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-26 10:13:55,142 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-26 10:13:55,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `batch_size` decimal(21,9) not null default 0, MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0
2025-06-26 10:13:55,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator Item` ADD COLUMN `allow_alternative_item` int(1) not null default 1
2025-06-26 10:13:55,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator Item` MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-26 10:13:55,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0
2025-06-26 10:13:55,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0
2025-06-26 10:13:56,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `for_quantity` decimal(21,9) not null default 0, MODIFY `time_required` decimal(21,9) not null default 0, MODIFY `total_time_in_mins` decimal(21,9) not null default 0
2025-06-26 10:13:56,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `ordered_qty` decimal(21,9) not null default 0
2025-06-26 10:13:56,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `wo_produced_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-26 10:13:56,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-26 10:13:56,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD INDEX `parent_detail_docname_index`(`parent_detail_docname`)
2025-06-26 10:13:56,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Default` ADD INDEX `default_warehouse_index`(`default_warehouse`)
2025-06-26 10:13:57,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-26 10:13:57,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-06-26 10:13:57,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `outgoing_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty_after_transaction` decimal(21,9) not null default 0
2025-06-26 10:13:57,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_date`, DROP INDEX `item_code`, DROP INDEX `warehouse`
2025-06-26 10:13:57,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-06-26 10:13:57,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` ADD COLUMN `has_corrective_cost` int(1) not null default 0
2025-06-26 10:13:57,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-06-26 10:13:57,956 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-26 10:13:57,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-06-26 10:13:58,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-26 10:13:58,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-06-26 10:13:58,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Bundle` MODIFY `naming_series` varchar(140) default 'SABB-.########', MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `avg_rate` decimal(21,9) not null default 0
2025-06-26 10:13:58,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` ADD COLUMN `total_weight` decimal(21,9) not null default 0
2025-06-26 10:13:58,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` MODIFY `shipment_amount` decimal(21,9) not null default 0, MODIFY `pickup_from` time(6) default '09:00', MODIFY `value_of_goods` decimal(21,9) not null default 0, MODIFY `pickup_to` time(6) default '17:00'
2025-06-26 10:13:58,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` ADD COLUMN `letter_head` varchar(140)
2025-06-26 10:13:58,991 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` MODIFY `sample_size` decimal(21,9) not null default 0
2025-06-26 10:13:59,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0
2025-06-26 10:13:59,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-26 10:13:59,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-26 10:14:00,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD COLUMN `recreate_stock_ledgers` int(1) not null default 0
2025-06-26 10:14:00,065 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD INDEX `creation`(`creation`)
2025-06-26 10:14:00,393 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `amount_difference_with_purchase_invoice` decimal(21,9) not null default 0
2025-06-26 10:14:00,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `received_stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-26 10:14:00,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` ADD COLUMN `sla_resolution_by` datetime(6), ADD COLUMN `sla_resolution_date` datetime(6)
2025-06-26 10:14:00,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `first_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9)
2025-06-26 10:14:00,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` ADD COLUMN `difference_account` varchar(140)
2025-06-26 10:14:00,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `new_asset_value` decimal(21,9) not null default 0, MODIFY `current_asset_value` decimal(21,9) not null default 0
2025-06-26 10:14:01,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category` ADD COLUMN `non_depreciable_category` int(1) not null default 0
2025-06-26 10:14:01,323 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0
2025-06-26 10:14:01,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD COLUMN `purchase_receipt_item` varchar(140)
2025-06-26 10:14:01,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-26 10:14:01,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD INDEX `creation`(`creation`)
2025-06-26 10:14:01,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` ADD COLUMN `subcontracting_conversion_factor` decimal(21,9) not null default 0, ADD COLUMN `job_card` varchar(140)
2025-06-26 10:14:01,784 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `service_cost_per_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `rm_cost_per_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-26 10:14:01,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-26 10:14:01,949 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `current_stock` decimal(21,9) not null default 0
2025-06-26 10:14:02,310 WARNING database DDL Query made to DB:
ALTER TABLE __UserSettings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
2025-06-26 10:14:02,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-06-26 10:14:02,847 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_warehouse`
2025-06-30 15:34:06,539 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-30 15:34:07,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstalled Application` ADD COLUMN `has_setup_wizard` int(1) not null default 0, ADD COLUMN `is_setup_complete` int(1) not null default 0
2025-06-30 15:34:07,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `grid_page_length` int(11) not null default 50, ADD COLUMN `protect_attached_files` int(1) not null default 0, ADD COLUMN `row_format` varchar(140) default 'Dynamic'
2025-06-30 15:34:08,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `currency` varchar(140), ADD COLUMN `background_color` varchar(140)
2025-06-30 15:34:08,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart` ADD COLUMN `currency` varchar(140)
2025-06-30 15:34:08,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Shortcut` ADD COLUMN `report_ref_doctype` varchar(140)
2025-06-30 15:34:08,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabReport` ADD COLUMN `add_translate_data` int(1) not null default 0
2025-06-30 15:34:08,784 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Format` ADD COLUMN `pdf_generator` varchar(140) default 'wkhtmltopdf'
2025-06-30 15:34:09,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `file_url_index`(file_url(100))
2025-06-30 15:34:09,488 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD INDEX `last_active_index`(`last_active`)
2025-06-30 15:34:09,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabSMS Parameter` MODIFY `value` varchar(255)
2025-06-30 15:34:11,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page View` ADD INDEX `path_index`(`path`)
2025-06-30 15:34:11,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Transition` ADD COLUMN `send_email_to_creator` int(1) not null default 0
2025-06-30 15:34:11,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` ADD COLUMN `send_email` int(1) not null default 1
2025-06-30 15:34:11,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Account` ADD COLUMN `always_bcc` varchar(140)
2025-06-30 15:34:12,499 WARNING database DDL Query made to DB:
ALTER TABLE `tabList View Settings` ADD COLUMN `disable_automatic_recency_filters` int(1) not null default 0
2025-06-30 15:34:12,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-06-30 15:34:13,571 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `link` text
2025-06-30 15:34:13,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent` MODIFY `google_meet_link` text
2025-06-30 15:34:14,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Request Log` MODIFY `url` text
2025-06-30 15:34:14,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoogle Calendar` ADD COLUMN `sync_as_public` int(1) not null default 0
2025-06-30 15:34:14,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-30 15:34:15,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-30 15:34:15,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0, MODIFY `withdrawal` decimal(21,9) not null default 0
2025-06-30 15:34:15,415 WARNING database DDL Query made to DB:
create table `tabPegged Currency Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_currency` varchar(140),
`pegged_against` varchar(140),
`pegged_exchange_rate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-30 15:34:15,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-06-30 15:34:16,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-30 15:34:16,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension` ADD INDEX `document_type_index`(`document_type`)
2025-06-30 15:34:16,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-30 15:34:16,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-30 15:34:17,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry` MODIFY `debit_in_transaction_currency` decimal(21,9) not null default 0, MODIFY `credit_in_transaction_currency` decimal(21,9) not null default 0, MODIFY `transaction_exchange_rate` decimal(21,9) not null default 0, MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0
2025-06-30 15:34:17,653 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `print_receipt_on_order_complete` int(1) not null default 0, ADD COLUMN `disable_grand_total_to_default_mop` int(1) not null default 0, ADD COLUMN `allow_partial_payment` int(1) not null default 0, ADD COLUMN `project` varchar(140)
2025-06-30 15:34:17,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-06-30 15:34:18,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-30 15:34:18,974 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0
2025-06-30 15:34:19,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `pos_invoice` varchar(140), ADD COLUMN `pos_invoice_item` varchar(140)
2025-06-30 15:34:19,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-06-30 15:34:19,438 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `pos_invoice_index`(`pos_invoice`), ADD INDEX `creation`(`creation`)
2025-06-30 15:34:19,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withheld Vouchers` MODIFY `taxable_amount` decimal(21,9) not null default 0
2025-06-30 15:34:19,714 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD COLUMN `base_outstanding` decimal(21,9) not null default 0, ADD COLUMN `base_paid_amount` decimal(21,9) not null default 0
2025-06-30 15:34:19,727 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` MODIFY `invoice_portion` decimal(21,9) not null default 0, MODIFY `payment_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `outstanding` decimal(21,9) not null default 0, MODIFY `base_payment_amount` decimal(21,9) not null default 0, MODIFY `discount` decimal(21,9) not null default 0
2025-06-30 15:34:19,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD INDEX `creation`(`creation`)
2025-06-30 15:34:20,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0
2025-06-30 15:34:20,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD INDEX `project_index`(`project`)
2025-06-30 15:34:20,718 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` ADD COLUMN `dont_enforce_free_item_qty` int(1) not null default 0
2025-06-30 15:34:20,735 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` MODIFY `free_item_rate` decimal(21,9) not null default 0, MODIFY `recurse_for` decimal(21,9) not null default 0, MODIFY `min_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `max_qty` decimal(21,9) not null default 0, MODIFY `threshold_percentage` decimal(21,9) not null default 0
2025-06-30 15:34:21,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-30 15:34:21,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-06-30 15:34:21,678 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` ADD COLUMN `categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)'
2025-06-30 15:34:22,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-06-30 15:34:22,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-06-30 15:34:22,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` ADD INDEX `creation`(`creation`)
2025-06-30 15:34:22,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-06-30 15:34:22,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-30 15:34:23,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` ADD COLUMN `subcontracted_quantity` decimal(21,9) not null default 0, ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-30 15:34:23,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-06-30 15:34:23,788 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0, ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-30 15:34:23,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-06-30 15:34:24,176 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-30 15:34:24,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-06-30 15:34:24,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-30 15:34:24,482 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-06-30 15:34:24,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject User` ADD COLUMN `hide_timesheets` int(1) not null default 0
2025-06-30 15:34:25,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-30 15:34:25,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0
2025-06-30 15:34:25,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD INDEX `project_index`(`project`)
2025-06-30 15:34:25,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-30 15:34:25,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2025-06-30 15:34:26,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-30 15:34:26,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-06-30 15:34:26,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `cost_center` varchar(140), ADD COLUMN `project` varchar(140)
2025-06-30 15:34:26,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `production_plan_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `produced_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `work_order_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-30 15:34:26,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD INDEX `project_index`(`project`)
2025-06-30 15:34:26,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-30 15:34:27,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-30 15:34:27,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `batch_size` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0
2025-06-30 15:34:27,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator Item` ADD COLUMN `allow_alternative_item` int(1) not null default 1
2025-06-30 15:34:27,438 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator Item` MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-30 15:34:27,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0
2025-06-30 15:34:27,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0
2025-06-30 15:34:28,245 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` MODIFY `time_required` decimal(21,9) not null default 0, MODIFY `total_time_in_mins` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `for_quantity` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0
2025-06-30 15:34:28,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `ordered_qty` decimal(21,9) not null default 0
2025-06-30 15:34:28,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `wo_produced_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0
2025-06-30 15:34:28,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0
2025-06-30 15:34:28,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD INDEX `parent_detail_docname_index`(`parent_detail_docname`)
2025-06-30 15:34:29,066 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Default` ADD INDEX `default_warehouse_index`(`default_warehouse`)
2025-06-30 15:34:29,323 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0
2025-06-30 15:34:29,354 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-06-30 15:34:29,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `outgoing_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `qty_after_transaction` decimal(21,9) not null default 0
2025-06-30 15:34:29,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_date_index`, DROP INDEX `warehouse`, DROP INDEX `item_code`
2025-06-30 15:34:29,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-06-30 15:34:29,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` ADD COLUMN `has_corrective_cost` int(1) not null default 0
2025-06-30 15:34:29,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-30 15:34:30,147 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-30 15:34:30,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-06-30 15:34:30,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-06-30 15:34:30,837 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `per_ordered` decimal(21,9) not null default 0
2025-06-30 15:34:31,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Bundle` MODIFY `avg_rate` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `naming_series` varchar(140) default 'SABB-.########', MODIFY `total_qty` decimal(21,9) not null default 0
2025-06-30 15:34:32,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` ADD COLUMN `total_weight` decimal(21,9) not null default 0
2025-06-30 15:34:33,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` MODIFY `pickup_from` time(6) default '09:00', MODIFY `value_of_goods` decimal(21,9) not null default 0, MODIFY `pickup_to` time(6) default '17:00', MODIFY `shipment_amount` decimal(21,9) not null default 0
2025-06-30 15:34:33,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` ADD COLUMN `letter_head` varchar(140)
2025-06-30 15:34:33,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` MODIFY `sample_size` decimal(21,9) not null default 0
2025-06-30 15:34:34,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-06-30 15:34:34,987 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-30 15:34:35,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-06-30 15:34:35,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD COLUMN `recreate_stock_ledgers` int(1) not null default 0
2025-06-30 15:34:35,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD INDEX `creation`(`creation`)
2025-06-30 15:34:35,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `amount_difference_with_purchase_invoice` decimal(21,9) not null default 0
2025-06-30 15:34:35,916 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `received_stock_qty` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0
2025-06-30 15:34:36,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` ADD COLUMN `sla_resolution_by` datetime(6), ADD COLUMN `sla_resolution_date` datetime(6)
2025-06-30 15:34:36,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `avg_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9)
2025-06-30 15:34:36,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` ADD COLUMN `difference_account` varchar(140)
2025-06-30 15:34:36,659 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` MODIFY `new_asset_value` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `current_asset_value` decimal(21,9) not null default 0
