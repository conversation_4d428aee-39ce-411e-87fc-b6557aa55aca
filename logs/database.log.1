2025-07-31 13:13:32,400 WARNING database DDL Query made to DB:
create table `tabServer Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`script_type` varchar(140),
`reference_doctype` varchar(140),
`event_frequency` varchar(140),
`cron_format` varchar(140),
`doctype_event` varchar(140),
`api_method` varchar(140),
`allow_guest` int(1) not null default 0,
`module` varchar(140),
`disabled` int(1) not null default 0,
`script` longtext,
`enable_rate_limit` int(1) not null default 0,
`rate_limit_count` int(11) not null default 5,
`rate_limit_seconds` int(11) not null default 86400,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index `module`(`module`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:32,487 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` ADD COLUMN `is_private` int(1) not null default 0, ADD COLUMN `file_type` varchar(140), ADD COLUMN `is_home_folder` int(1) not null default 0, ADD COLUMN `is_attachments_folder` int(1) not null default 0, ADD COLUMN `thumbnail_url` text, ADD COLUMN `folder` varchar(255), ADD COLUMN `is_folder` int(1) not null default 0, ADD COLUMN `attached_to_field` varchar(140), ADD COLUMN `old_parent` varchar(140), ADD COLUMN `content_hash` varchar(140), ADD COLUMN `uploaded_to_dropbox` int(1) not null default 0, ADD COLUMN `uploaded_to_google_drive` int(1) not null default 0, ADD COLUMN `_user_tags` text, ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-07-31 13:13:32,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` MODIFY `file_name` varchar(140), MODIFY `attached_to_doctype` varchar(140), MODIFY `file_size` bigint(20) not null default 0, MODIFY `attached_to_name` varchar(140), MODIFY `file_url` longtext
2025-07-31 13:13:32,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile` DROP INDEX `attached_to_name`, DROP INDEX `attached_to_doctype`
2025-07-31 13:13:32,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `attached_to_doctype_attached_to_name_index`(attached_to_doctype, attached_to_name)
2025-07-31 13:13:32,608 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `file_url_index`(file_url(100))
2025-07-31 13:13:32,665 WARNING database DDL Query made to DB:
create table `tabReport Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`label` varchar(140),
`fieldtype` varchar(140),
`options` varchar(140),
`width` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:32,714 WARNING database DDL Query made to DB:
create table `tabBlock Module` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:32,798 WARNING database DDL Query made to DB:
create table `tabCustom DocPerm` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent` varchar(140),
`role` varchar(140),
`if_owner` int(1) not null default 0,
`permlevel` int(11) not null default 0,
`select` int(1) not null default 0,
`read` int(1) not null default 1,
`write` int(1) not null default 0,
`create` int(1) not null default 0,
`delete` int(1) not null default 0,
`submit` int(1) not null default 0,
`cancel` int(1) not null default 0,
`amend` int(1) not null default 0,
`report` int(1) not null default 0,
`export` int(1) not null default 1,
`import` int(1) not null default 0,
`share` int(1) not null default 0,
`print` int(1) not null default 0,
`email` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `parent`(`parent`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,028 WARNING database DDL Query made to DB:
create table `tabUser` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`email` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`username` varchar(140) unique,
`language` varchar(140),
`time_zone` varchar(140),
`send_welcome_email` int(1) not null default 1,
`unsubscribed` int(1) not null default 0,
`user_image` text,
`role_profile_name` varchar(140),
`module_profile` varchar(140),
`home_settings` longtext,
`gender` varchar(140),
`birth_date` date,
`interest` text,
`phone` varchar(140),
`location` varchar(140),
`bio` text,
`mobile_no` varchar(140) unique,
`mute_sounds` int(1) not null default 0,
`desk_theme` varchar(140),
`banner_image` text,
`search_bar` int(1) not null default 1,
`notifications` int(1) not null default 1,
`list_sidebar` int(1) not null default 1,
`bulk_actions` int(1) not null default 1,
`view_switcher` int(1) not null default 1,
`form_sidebar` int(1) not null default 1,
`timeline` int(1) not null default 1,
`dashboard` int(1) not null default 1,
`new_password` text,
`logout_all_sessions` int(1) not null default 1,
`reset_password_key` varchar(140),
`last_reset_password_key_generated_on` datetime(6),
`last_password_reset_date` date,
`redirect_url` text,
`document_follow_notify` int(1) not null default 0,
`document_follow_frequency` varchar(140) default 'Daily',
`follow_created_documents` int(1) not null default 0,
`follow_commented_documents` int(1) not null default 0,
`follow_liked_documents` int(1) not null default 0,
`follow_assigned_documents` int(1) not null default 0,
`follow_shared_documents` int(1) not null default 0,
`email_signature` text,
`thread_notify` int(1) not null default 1,
`send_me_a_copy` int(1) not null default 0,
`allowed_in_mentions` int(1) not null default 1,
`default_workspace` varchar(140),
`default_app` varchar(140),
`simultaneous_sessions` int(11) not null default 2,
`restrict_ip` text,
`last_ip` varchar(140),
`login_after` int(11) not null default 0,
`user_type` varchar(140) default 'System User',
`last_active` datetime(6),
`login_before` int(11) not null default 0,
`bypass_restrict_ip_check_if_2fa_enabled` int(1) not null default 0,
`last_login` varchar(140),
`last_known_versions` text,
`api_key` varchar(140) unique,
`api_secret` text,
`onboarding_status` text default '{}',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `last_active`(`last_active`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,106 WARNING database DDL Query made to DB:
create table `tabPrepared Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Queued',
`report_name` varchar(140),
`job_id` varchar(140),
`report_end_time` datetime(6),
`peak_memory_usage` int(11) not null default 0,
`error_message` text,
`filters` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index `report_name`(`report_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,209 WARNING database DDL Query made to DB:
create table `tabTransaction Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`row_index` varchar(140),
`reference_doctype` varchar(140),
`document_name` varchar(140),
`timestamp` datetime(6),
`checksum_version` varchar(140),
`previous_hash` text,
`transaction_hash` text,
`chaining_hash` text,
`data` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,316 WARNING database DDL Query made to DB:
create table `tabTranslation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`contributed` int(1) not null default 0,
`language` varchar(140),
`source_text` longtext,
`context` varchar(140),
`translated_text` longtext,
`contribution_status` varchar(140),
`contribution_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `language`(`language`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,507 WARNING database DDL Query made to DB:
create table `tabScheduled Job Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`scheduled_job_type` varchar(140),
`details` longtext,
`debug_log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,569 WARNING database DDL Query made to DB:
create table `tabUser Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`role` varchar(140),
`apply_user_permission_on` varchar(140),
`user_id_field` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,626 WARNING database DDL Query made to DB:
create table `tabUser Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_account` varchar(140),
`email_id` varchar(140),
`awaiting_password` int(1) not null default 0,
`used_oauth` int(1) not null default 0,
`enable_outgoing` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,676 WARNING database DDL Query made to DB:
create table `tabSMS Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140),
`value` varchar(255),
`header` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,730 WARNING database DDL Query made to DB:
create table `tabUser Group Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,785 WARNING database DDL Query made to DB:
create table `tabUser Social Login` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`provider` varchar(140),
`username` varchar(140),
`userid` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,841 WARNING database DDL Query made to DB:
create table `tabPatch Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patch` longtext,
`skipped` int(1) not null default 0,
`traceback` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,892 WARNING database DDL Query made to DB:
create table `tabHas Domain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`domain` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:33,951 WARNING database DDL Query made to DB:
create table `tabUser Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,002 WARNING database DDL Query made to DB:
create table `tabScheduler Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheduled_against` varchar(140),
`method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,054 WARNING database DDL Query made to DB:
create table `tabData Import Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`data_import` varchar(140),
`row_indexes` longtext,
`success` int(1) not null default 0,
`docname` varchar(140),
`messages` longtext,
`exception` text,
`log_index` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,113 WARNING database DDL Query made to DB:
create table `tabRole Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role_profile` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,169 WARNING database DDL Query made to DB:
create table `tabReport Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldtype` varchar(140),
`fieldname` varchar(140),
`mandatory` int(1) not null default 0,
`wildcard_filter` int(1) not null default 0,
`options` text,
`default` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,222 WARNING database DDL Query made to DB:
create table `tabCommunication Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`link_doctype` varchar(140),
`link_name` varchar(140),
`link_title` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication Link`
				ADD INDEX IF NOT EXISTS `link_doctype_link_name_index`(link_doctype, link_name)
2025-07-31 13:13:34,403 WARNING database DDL Query made to DB:
create table `tabDeleted Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`deleted_name` varchar(140),
`deleted_doctype` varchar(140),
`restored` int(1) not null default 0,
`new_name` varchar(140),
`data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,473 WARNING database DDL Query made to DB:
create table `tabAccess Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`export_from` varchar(140),
`user` varchar(140),
`reference_document` varchar(140),
`timestamp` datetime(6),
`file_type` varchar(140),
`method` varchar(140),
`report_name` varchar(140),
`filters` longtext,
`page` longtext,
`columns` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,555 WARNING database DDL Query made to DB:
create table `tabDocShare` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`share_doctype` varchar(140),
`share_name` varchar(140),
`read` int(1) not null default 0,
`write` int(1) not null default 0,
`share` int(1) not null default 0,
`submit` int(1) not null default 0,
`everyone` int(1) not null default 0,
`notify_by_email` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `share_doctype`(`share_doctype`),
index `share_name`(`share_name`),
index `everyone`(`everyone`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocShare`
				ADD INDEX IF NOT EXISTS `user_share_doctype_index`(user, share_doctype)
2025-07-31 13:13:34,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocShare`
				ADD INDEX IF NOT EXISTS `share_doctype_share_name_index`(share_doctype, share_name)
2025-07-31 13:13:34,784 WARNING database DDL Query made to DB:
create table `tabSubmission Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`job_id` varchar(140),
`ended_at` datetime(6),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`exception` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ref_docname`(`ref_docname`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,849 WARNING database DDL Query made to DB:
create table `tabDocument Share Key` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`key` varchar(140),
`expires_on` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index `reference_docname`(`reference_docname`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,896 WARNING database DDL Query made to DB:
create table `tabUser Type Module` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:34,955 WARNING database DDL Query made to DB:
create table `tabLogs To Clear` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`days` int(11) not null default 30,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,031 WARNING database DDL Query made to DB:
create table `tabData Import` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`import_type` varchar(140),
`import_file` text,
`payload_count` int(11) not null default 0,
`google_sheets_url` varchar(140),
`status` varchar(140) default 'Pending',
`submit_after_import` int(1) not null default 0,
`mute_emails` int(1) not null default 1,
`template_options` longtext,
`template_warnings` longtext,
`show_failed_logs` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,085 WARNING database DDL Query made to DB:
create table `tabVersion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`docname` varchar(140),
`data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabVersion`
				ADD INDEX IF NOT EXISTS `ref_doctype_docname_index`(ref_doctype, docname)
2025-07-31 13:13:35,164 WARNING database DDL Query made to DB:
create table `tabDocument Naming Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`disabled` int(1) not null default 0,
`priority` int(11) not null default 0,
`prefix` varchar(140),
`counter` int(11) not null default 0,
`prefix_digits` int(11) not null default 5,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,225 WARNING database DDL Query made to DB:
create table `tabNavbar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_label` varchar(140),
`item_type` varchar(140),
`route` varchar(140),
`action` varchar(140),
`hidden` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,288 WARNING database DDL Query made to DB:
create table `tabLanguage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`language_code` varchar(140) unique,
`language_name` varchar(140),
`flag` varchar(140),
`based_on` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,542 WARNING database DDL Query made to DB:
create table `tabUser Select Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,600 WARNING database DDL Query made to DB:
create table `tabAmended Document Naming Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`action` varchar(140) default 'Amend Counter',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,659 WARNING database DDL Query made to DB:
create table `tabDomain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`domain` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,719 WARNING database DDL Query made to DB:
create table `tabModule Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module_profile_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,810 WARNING database DDL Query made to DB:
create table `tabDocument Naming Rule Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`field` varchar(140),
`condition` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,868 WARNING database DDL Query made to DB:
create table `tabError Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`seen` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`method` varchar(140),
`error` longtext,
`trace_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:35,971 WARNING database DDL Query made to DB:
create table `tabPackage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package_name` varchar(140),
`readme` longtext,
`license_type` varchar(140),
`license` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:36,206 WARNING database DDL Query made to DB:
create table `tabUser Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`allow` varchar(140),
`for_value` varchar(140),
`is_default` int(1) not null default 0,
`apply_to_all_doctypes` int(1) not null default 1,
`applicable_for` varchar(140),
`hide_descendants` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:36,316 WARNING database DDL Query made to DB:
create table `tabCommunication` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`communication_medium` varchar(140),
`sender` varchar(255),
`recipients` longtext,
`cc` longtext,
`bcc` longtext,
`phone_no` varchar(140),
`delivery_status` varchar(140),
`content` longtext,
`text_content` longtext,
`communication_type` varchar(140) default 'Communication',
`comment_type` varchar(140),
`status` varchar(140),
`sent_or_received` varchar(140),
`communication_date` datetime(6),
`read_receipt` int(1) not null default 0,
`send_after` datetime(6),
`sender_full_name` varchar(140),
`read_by_recipient` int(1) not null default 0,
`read_by_recipient_on` datetime(6),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`email_account` varchar(140),
`in_reply_to` varchar(140),
`user` varchar(140),
`email_template` varchar(140),
`unread_notification_sent` int(1) not null default 0,
`seen` int(1) not null default 0,
`_user_tags` text,
`message_id` text,
`uid` int(11) not null default 0,
`imap_folder` varchar(140),
`email_status` varchar(140),
`has_attachment` int(1) not null default 0,
`rating` int(11) not null default 0,
`feedback_request` varchar(140),
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `reference_owner`(`reference_owner`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:36,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-07-31 13:13:36,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `status_communication_type_index`(status, communication_type)
2025-07-31 13:13:36,488 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication`
				ADD INDEX IF NOT EXISTS `message_id_index`(message_id(140))
2025-07-31 13:13:36,561 WARNING database DDL Query made to DB:
create table `tabInstalled Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`app_name` varchar(140),
`app_version` varchar(140),
`git_branch` varchar(140),
`has_setup_wizard` int(1) not null default 0,
`is_setup_complete` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:36,611 WARNING database DDL Query made to DB:
create table `tabSession Default` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:36,698 WARNING database DDL Query made to DB:
create table `tabActivity Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`content` longtext,
`communication_date` datetime(6),
`ip_address` varchar(140),
`operation` varchar(140),
`status` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`timeline_doctype` varchar(140),
`timeline_name` varchar(140),
`link_doctype` varchar(140),
`link_name` varchar(140),
`user` varchar(140),
`full_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:36,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-07-31 13:13:36,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log`
				ADD INDEX IF NOT EXISTS `timeline_doctype_timeline_name_index`(timeline_doctype, timeline_name)
2025-07-31 13:13:36,801 WARNING database DDL Query made to DB:
create table `tabPackage Import` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attach_package` text,
`activate` int(1) not null default 0,
`force` int(1) not null default 0,
`log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:36,864 WARNING database DDL Query made to DB:
create table `tabComment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`comment_type` varchar(140) default 'Comment',
`comment_email` varchar(140),
`subject` text,
`comment_by` varchar(140),
`published` int(1) not null default 0,
`seen` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`content` longtext,
`ip_address` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:36,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabComment`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-07-31 13:13:36,991 WARNING database DDL Query made to DB:
create table `tabView Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`viewed_by` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:37,060 WARNING database DDL Query made to DB:
create table `tabScheduled Job Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`stopped` int(1) not null default 0,
`method` varchar(140),
`server_script` varchar(140),
`scheduler_event` varchar(140),
`frequency` varchar(140),
`cron_format` varchar(140),
`create_log` int(1) not null default 0,
`last_execution` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `server_script`(`server_script`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:37,117 WARNING database DDL Query made to DB:
create table `tabLog Setting User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140) unique,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:37,184 WARNING database DDL Query made to DB:
create table `tabCustom Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`page` varchar(140),
`report` varchar(140),
`ref_doctype` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:37,233 WARNING database DDL Query made to DB:
create table `tabDynamic Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`link_doctype` varchar(140),
`link_name` varchar(140),
`link_title` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:37,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabDynamic Link`
				ADD INDEX IF NOT EXISTS `link_doctype_link_name_index`(link_doctype, link_name)
2025-07-31 13:13:37,324 WARNING database DDL Query made to DB:
create table `tabModule Def` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module_name` varchar(140) unique,
`custom` int(1) not null default 0,
`package` varchar(140),
`app_name` varchar(140),
`restrict_to_domain` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:37,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue` MODIFY `defkey` varchar(140)
2025-07-31 13:13:37,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue`
				ADD INDEX IF NOT EXISTS `defaultvalue_parent_parenttype_index`(parent, parenttype)
2025-07-31 13:13:37,676 WARNING database DDL Query made to DB:
create table `tabPackage Release` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package` varchar(140),
`publish` int(1) not null default 0,
`path` text,
`major` int(11) not null default 0,
`minor` int(11) not null default 0,
`patch` int(11) not null default 0,
`release_notes` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:37,744 WARNING database DDL Query made to DB:
create table `tabUser Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`is_custom` int(1) not null default 0,
`read` int(1) not null default 1,
`write` int(1) not null default 0,
`create` int(1) not null default 0,
`submit` int(1) not null default 0,
`cancel` int(1) not null default 0,
`amend` int(1) not null default 0,
`delete` int(1) not null default 0,
`email` int(1) not null default 1,
`share` int(1) not null default 1,
`print` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:37,858 WARNING database DDL Query made to DB:
create table `tabSuccess Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140) unique,
`first_success_message` varchar(140) default 'Congratulations on first creations',
`message` varchar(140) default 'Successfully created',
`next_actions` varchar(140),
`action_timeout` int(11) not null default 7,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,405 WARNING database DDL Query made to DB:
create table `tabPersonal Data Deletion Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`status` varchar(140),
`partial` int(1) not null default 0,
`fields` text,
`filtered_by` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,455 WARNING database DDL Query made to DB:
create table `tabWebsite Sidebar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140),
`group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,507 WARNING database DDL Query made to DB:
create table `tabWebsite Theme Ignore App` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`app` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,621 WARNING database DDL Query made to DB:
create table `tabPersonal Data Deletion Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email` varchar(140),
`status` varchar(140) default 'Pending Verification',
`anonymization_matrix` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,676 WARNING database DDL Query made to DB:
create table `tabSocial Link Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`social_link_type` varchar(140),
`color` varchar(140),
`background_color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,744 WARNING database DDL Query made to DB:
create table `tabWebsite Slideshow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`slideshow_name` varchar(140) unique,
`header` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,794 WARNING database DDL Query made to DB:
create table `tabCompany History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`year` varchar(140),
`highlight` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,850 WARNING database DDL Query made to DB:
create table `tabColor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,911 WARNING database DDL Query made to DB:
create table `tabWebsite Slideshow Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`image` text,
`heading` varchar(140),
`description` text,
`url` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:38,967 WARNING database DDL Query made to DB:
create table `tabWebsite Sidebar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,025 WARNING database DDL Query made to DB:
create table `tabAbout Us Team Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`full_name` varchar(140),
`image_link` text,
`bio` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,078 WARNING database DDL Query made to DB:
create table `tabWebsite Route Redirect` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source` text,
`target` text,
`redirect_http_status` varchar(140) default '301',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,134 WARNING database DDL Query made to DB:
create table `tabPersonal Data Download Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`user_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,254 WARNING database DDL Query made to DB:
create table `tabBlog Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`published` int(1) not null default 1,
`title` varchar(140),
`description` text,
`route` varchar(140) unique,
`preview_image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,391 WARNING database DDL Query made to DB:
create table `tabHelp Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`category_description` text,
`published` int(1) not null default 0,
`help_articles` int(11) not null default 0,
`route` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,485 WARNING database DDL Query made to DB:
create table `tabBlog Post` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`blog_category` varchar(140),
`blogger` varchar(140),
`route` varchar(140) unique,
`read_time` int(11) not null default 0,
`published_on` date,
`published` int(1) not null default 0,
`featured` int(1) not null default 0,
`hide_cta` int(1) not null default 0,
`enable_email_notification` int(1) not null default 1,
`disable_comments` int(1) not null default 0,
`disable_likes` int(1) not null default 0,
`blog_intro` text,
`content_type` varchar(140) default 'Markdown',
`content` longtext,
`content_md` longtext,
`content_html` longtext,
`email_sent` int(1) not null default 0,
`meta_title` varchar(60),
`meta_description` text,
`meta_image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,554 WARNING database DDL Query made to DB:
create table `tabWeb Page View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`path` varchar(140),
`referrer` varchar(140),
`browser` varchar(140),
`browser_version` varchar(140),
`is_unique` varchar(140),
`time_zone` varchar(140),
`user_agent` varchar(140),
`source` varchar(140),
`campaign` varchar(140),
`medium` varchar(140),
`visitor_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `path`(`path`),
index `visitor_id`(`visitor_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,611 WARNING database DDL Query made to DB:
create table `tabMarketing Campaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`campaign_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,684 WARNING database DDL Query made to DB:
create table `tabWeb Page Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`web_template` varchar(140),
`web_template_values` longtext,
`css_class` text,
`section_id` varchar(140),
`add_container` int(1) not null default 1,
`add_top_padding` int(1) not null default 1,
`add_bottom_padding` int(1) not null default 1,
`add_border_at_top` int(1) not null default 0,
`add_border_at_bottom` int(1) not null default 0,
`add_shade` int(1) not null default 0,
`hide_block` int(1) not null default 0,
`add_background_image` int(1) not null default 0,
`background_image` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,739 WARNING database DDL Query made to DB:
create table `tabWeb Template Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
`fieldtype` varchar(140) default 'Data',
`reqd` int(1) not null default 0,
`options` text,
`default` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,907 WARNING database DDL Query made to DB:
create table `tabWebsite Meta Tag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key` varchar(140),
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:39,958 WARNING database DDL Query made to DB:
create table `tabDiscussion Reply` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`topic` varchar(140),
`reply` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:40,020 WARNING database DDL Query made to DB:
create table `tabTop Bar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`url` varchar(140),
`open_in_new_tab` int(1) not null default 0,
`right` int(1) not null default 1,
`parent_label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:40,129 WARNING database DDL Query made to DB:
create sequence if not exists web_form_list_column_id_seq nocache nocycle
2025-07-31 13:13:40,146 WARNING database DDL Query made to DB:
create table `tabWeb Form List Column` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:40,214 WARNING database DDL Query made to DB:
create table `tabBlogger` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`short_name` varchar(140) unique,
`full_name` varchar(140),
`user` varchar(140),
`bio` text,
`avatar` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:40,267 WARNING database DDL Query made to DB:
create table `tabWebsite Route Meta` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:40,338 WARNING database DDL Query made to DB:
create table `tabHelp Article` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`category` varchar(140),
`published` int(1) not null default 0,
`author` varchar(140) default 'user_fullname',
`level` varchar(140),
`content` longtext,
`likes` int(11) not null default 0,
`route` varchar(140),
`helpful` int(11) not null default 0,
`not_helpful` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:40,402 WARNING database DDL Query made to DB:
create table `tabDiscussion Topic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,080 WARNING database DDL Query made to DB:
create table `tabWorkflow Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`reference_name` varchar(140),
`reference_doctype` varchar(140),
`workflow_state` varchar(140),
`completed_by_role` varchar(140),
`completed_by` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Action`
				ADD INDEX IF NOT EXISTS `reference_name_reference_doctype_status_index`(reference_name, reference_doctype, status)
2025-07-31 13:13:41,292 WARNING database DDL Query made to DB:
create table `tabWorkflow Action Master` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workflow_action_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,368 WARNING database DDL Query made to DB:
create table `tabWorkflow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workflow_name` varchar(140) unique,
`document_type` varchar(140),
`is_active` int(1) not null default 0,
`override_status` int(1) not null default 0,
`send_email_alert` int(1) not null default 0,
`workflow_state_field` varchar(140) default 'workflow_state',
`workflow_data` json,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,421 WARNING database DDL Query made to DB:
create table `tabWorkflow Action Permitted Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,487 WARNING database DDL Query made to DB:
create table `tabWorkflow State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workflow_state_name` varchar(140) unique,
`icon` varchar(140),
`style` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,557 WARNING database DDL Query made to DB:
create table `tabWorkflow Transition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`state` varchar(140),
`action` varchar(140),
`next_state` varchar(140),
`allowed` varchar(140),
`allow_self_approval` int(1) not null default 1,
`send_email_to_creator` int(1) not null default 0,
`condition` longtext,
`workflow_builder_id` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,627 WARNING database DDL Query made to DB:
create table `tabWorkflow Document State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`state` varchar(140),
`doc_status` varchar(140) default '0',
`update_field` varchar(140),
`update_value` varchar(140),
`is_optional_state` int(1) not null default 0,
`avoid_status_override` int(1) not null default 0,
`next_action_email_template` varchar(140),
`allow_edit` varchar(140),
`send_email` int(1) not null default 1,
`message` text,
`workflow_builder_id` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,764 WARNING database DDL Query made to DB:
create table `tabEmail Queue Recipient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`recipient` varchar(140),
`status` varchar(140) default 'Not Sent',
`error` longtext,
index `status`(`status`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:41,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue Recipient`
				ADD INDEX IF NOT EXISTS `modified_index`(modified)
2025-07-31 13:13:41,984 WARNING database DDL Query made to DB:
create table `tabEmail Group Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_group` varchar(140),
`email` varchar(140),
`unsubscribed` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email_group`(`email_group`),
index `unsubscribed`(`unsubscribed`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,023 WARNING database DDL Query made to DB:
alter table `tabEmail Group Member`
					add unique `unique_email_group_email`(email_group, email)
2025-07-31 13:13:42,154 WARNING database DDL Query made to DB:
create table `tabEmail Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_id` varchar(140) unique,
`email_account_name` varchar(140) unique,
`domain` varchar(140),
`service` varchar(140),
`auth_method` varchar(140) default 'Basic',
`backend_app_flow` int(1) not null default 0,
`password` text,
`awaiting_password` int(1) not null default 0,
`ascii_encode_password` int(1) not null default 0,
`connected_app` varchar(140),
`connected_user` varchar(140),
`login_id_is_different` int(1) not null default 0,
`login_id` varchar(140),
`enable_incoming` int(1) not null default 0,
`default_incoming` int(1) not null default 0,
`use_imap` int(1) not null default 0,
`use_ssl` int(1) not null default 0,
`use_starttls` int(1) not null default 0,
`email_server` varchar(140),
`incoming_port` varchar(140),
`attachment_limit` int(11) not null default 0,
`email_sync_option` varchar(140) default 'UNSEEN',
`initial_sync_count` varchar(140) default '250',
`append_emails_to_sent_folder` int(1) not null default 0,
`sent_folder_name` varchar(140),
`append_to` varchar(140),
`create_contact` int(1) not null default 1,
`enable_automatic_linking` int(1) not null default 0,
`notify_if_unreplied` int(1) not null default 0,
`unreplied_for_mins` int(11) not null default 30,
`send_notification_to` text,
`enable_outgoing` int(1) not null default 0,
`use_tls` int(1) not null default 0,
`use_ssl_for_outgoing` int(1) not null default 0,
`smtp_server` varchar(140),
`smtp_port` varchar(140),
`default_outgoing` int(1) not null default 0,
`always_use_account_email_id_as_sender` int(1) not null default 0,
`always_use_account_name_as_sender_name` int(1) not null default 0,
`send_unsubscribe_message` int(1) not null default 1,
`track_email_status` int(1) not null default 1,
`no_smtp_authentication` int(1) not null default 0,
`always_bcc` varchar(140),
`add_signature` int(1) not null default 0,
`signature` longtext,
`enable_auto_reply` int(1) not null default 0,
`auto_reply_message` longtext,
`footer` longtext,
`brand_logo` text,
`uidvalidity` varchar(140),
`uidnext` int(11) not null default 0,
`no_failed` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,226 WARNING database DDL Query made to DB:
create table `tabEmail Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`total_subscribers` int(11) not null default 0,
`confirmation_email_template` varchar(140),
`welcome_email_template` varchar(140),
`welcome_url` varchar(140),
`add_query_parameters` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,279 WARNING database DDL Query made to DB:
create table `tabNewsletter Attachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,355 WARNING database DDL Query made to DB:
create table `tabDocument Follow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `user`(`user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,440 WARNING database DDL Query made to DB:
create table `tabEmail Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sender` varchar(140),
`show_as_cc` text,
`message` longtext,
`status` varchar(140) default 'Not Sent',
`error` longtext,
`message_id` text,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`communication` varchar(140),
`send_after` datetime(6),
`priority` int(11) not null default 1,
`add_unsubscribe_link` int(1) not null default 1,
`unsubscribe_param` varchar(140),
`unsubscribe_method` varchar(140),
`expose_recipients` varchar(140),
`attachments` longtext,
`retry` int(11) not null default 0,
`email_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index `communication`(`communication`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,473 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue`
				ADD INDEX IF NOT EXISTS `index_bulk_flush`(status, send_after, priority, creation)
2025-07-31 13:13:42,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue`
				ADD INDEX IF NOT EXISTS `message_id_index`(message_id(140))
2025-07-31 13:13:42,568 WARNING database DDL Query made to DB:
create table `tabEmail Domain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`domain_name` varchar(140) unique,
`email_server` varchar(140),
`use_imap` int(1) not null default 0,
`use_ssl` int(1) not null default 0,
`use_starttls` int(1) not null default 0,
`incoming_port` varchar(140),
`attachment_limit` int(11) not null default 0,
`smtp_server` varchar(140),
`use_tls` int(1) not null default 0,
`use_ssl_for_outgoing` int(1) not null default 0,
`smtp_port` varchar(140),
`append_emails_to_sent_folder` int(1) not null default 0,
`sent_folder_name` varchar(140) default 'Sent',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,634 WARNING database DDL Query made to DB:
create table `tabEmail Unsubscribe` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`global_unsubscribe` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email`(`email`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,691 WARNING database DDL Query made to DB:
create table `tabEmail Flag Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_completed` int(1) not null default 0,
`communication` varchar(140),
`action` varchar(140),
`email_account` varchar(140),
`uid` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,786 WARNING database DDL Query made to DB:
create table `tabNewsletter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_sent_at` datetime(6),
`total_recipients` int(11) not null default 0,
`total_views` int(11) not null default 0,
`email_sent` int(1) not null default 0,
`sender_name` varchar(140),
`sender_email` varchar(140),
`send_from` varchar(140),
`subject` text,
`content_type` varchar(140),
`message` longtext,
`message_md` longtext,
`message_html` longtext,
`campaign` varchar(140),
`send_unsubscribe_link` int(1) not null default 1,
`send_webview_link` int(1) not null default 0,
`scheduled_to_send` int(11) not null default 0,
`schedule_sending` int(1) not null default 0,
`schedule_send` datetime(6),
`published` int(1) not null default 0,
`route` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,846 WARNING database DDL Query made to DB:
create table `tabNotification Recipient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`receiver_by_document_field` varchar(140),
`receiver_by_role` varchar(140),
`cc` longtext,
`bcc` longtext,
`condition` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,905 WARNING database DDL Query made to DB:
create table `tabEmail Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`use_html` int(1) not null default 0,
`response_html` longtext,
`response` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:42,963 WARNING database DDL Query made to DB:
create table `tabUnhandled Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_account` varchar(140),
`uid` varchar(140),
`reason` longtext,
`message_id` longtext,
`raw` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:43,048 WARNING database DDL Query made to DB:
create table `tabAuto Email Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report` varchar(140),
`user` varchar(140) default 'User',
`enabled` int(1) not null default 1,
`report_type` varchar(140),
`reference_report` varchar(140),
`send_if_data` int(1) not null default 1,
`data_modified_till` int(11) not null default 0,
`no_of_rows` int(11) not null default 100,
`filters` text,
`filter_meta` text,
`from_date_field` varchar(140),
`to_date_field` varchar(140),
`dynamic_date_period` varchar(140),
`use_first_day_of_period` int(1) not null default 0,
`email_to` text,
`day_of_week` varchar(140) default 'Monday',
`sender` varchar(140),
`frequency` varchar(140),
`format` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:43,108 WARNING database DDL Query made to DB:
create table `tabIMAP Folder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`folder_name` varchar(140),
`append_to` varchar(140),
`uidvalidity` varchar(140),
`uidnext` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:43,159 WARNING database DDL Query made to DB:
create table `tabNewsletter Email Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_group` varchar(140),
`total_subscribers` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:43,223 WARNING database DDL Query made to DB:
create table `tabEmail Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_id` varchar(140) unique,
`is_spam` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:43,278 WARNING database DDL Query made to DB:
create table `tabDocType Layout` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`route` varchar(140),
`client_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:43,473 WARNING database DDL Query made to DB:
create table `tabDocType Layout Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:43,619 WARNING database DDL Query made to DB:
create table `tabCustomize Form Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`label` varchar(140),
`fieldtype` varchar(140) default 'Data',
`fieldname` varchar(140),
`non_negative` int(1) not null default 0,
`reqd` int(1) not null default 0,
`unique` int(1) not null default 0,
`is_virtual` int(1) not null default 0,
`in_list_view` int(1) not null default 0,
`in_standard_filter` int(1) not null default 0,
`in_global_search` int(1) not null default 0,
`in_preview` int(1) not null default 0,
`bold` int(1) not null default 0,
`no_copy` int(1) not null default 0,
`allow_in_quick_entry` int(1) not null default 0,
`translatable` int(1) not null default 1,
`link_filters` json,
`default` text,
`precision` varchar(140),
`length` int(11) not null default 0,
`options` text,
`sort_options` int(1) not null default 0,
`fetch_from` text,
`fetch_if_empty` int(1) not null default 0,
`show_dashboard` int(1) not null default 0,
`depends_on` longtext,
`permlevel` int(11) not null default 0,
`hidden` int(1) not null default 0,
`read_only` int(1) not null default 0,
`collapsible` int(1) not null default 0,
`allow_bulk_edit` int(1) not null default 0,
`collapsible_depends_on` longtext,
`ignore_user_permissions` int(1) not null default 0,
`allow_on_submit` int(1) not null default 0,
`report_hide` int(1) not null default 0,
`remember_last_selected_value` int(1) not null default 0,
`hide_border` int(1) not null default 0,
`ignore_xss_filter` int(1) not null default 0,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`in_filter` int(1) not null default 0,
`hide_seconds` int(1) not null default 0,
`hide_days` int(1) not null default 0,
`description` text,
`placeholder` varchar(140),
`print_hide` int(1) not null default 0,
`print_hide_if_no_value` int(1) not null default 0,
`print_width` varchar(140),
`columns` int(11) not null default 0,
`width` varchar(140),
`is_custom_field` int(1) not null default 0,
index `label`(`label`),
index `fieldtype`(`fieldtype`),
index `fieldname`(`fieldname`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:44,008 WARNING database DDL Query made to DB:
create table `tabCurrency` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`currency_name` varchar(140) unique,
`enabled` int(1) not null default 0,
`fraction` varchar(140),
`fraction_units` int(11) not null default 0,
`smallest_currency_fraction_value` decimal(21,9) not null default 0,
`symbol` varchar(140),
`symbol_on_right` int(1) not null default 0,
`number_format` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:44,266 WARNING database DDL Query made to DB:
create table `tabCountry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country_name` varchar(140) unique,
`date_format` varchar(140),
`time_format` varchar(140) default 'HH:mm:ss',
`time_zones` text,
`code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:44,584 WARNING database DDL Query made to DB:
create table `tabCalendar View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`subject_field` varchar(140),
`start_date_field` varchar(140),
`end_date_field` varchar(140),
`all_day` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:44,658 WARNING database DDL Query made to DB:
create table `tabCustom HTML Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`private` int(1) not null default 0,
`html` longtext,
`script` longtext,
`style` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:44,773 WARNING database DDL Query made to DB:
create table `tabList View Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disable_count` int(1) not null default 0,
`disable_comment_count` int(1) not null default 0,
`disable_sidebar_stats` int(1) not null default 0,
`disable_auto_refresh` int(1) not null default 0,
`allow_edit` int(1) not null default 0,
`disable_automatic_recency_filters` int(1) not null default 0,
`total_fields` varchar(140),
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:44,845 WARNING database DDL Query made to DB:
create table `tabTag Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`tag` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:44,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-07-31 13:13:44,948 WARNING database DDL Query made to DB:
create table `tabNote` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`public` int(1) not null default 0,
`notify_on_login` int(1) not null default 0,
`notify_on_every_login` int(1) not null default 0,
`expire_notification_on` date,
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `expire_notification_on`(`expire_notification_on`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,033 WARNING database DDL Query made to DB:
create table `tabNotification Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`for_user` varchar(140),
`type` varchar(140),
`email_content` longtext,
`document_type` varchar(140),
`read` int(1) not null default 0,
`document_name` varchar(140),
`attached_file` longtext,
`from_user` varchar(140),
`link` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `for_user`(`for_user`),
index `document_name`(`document_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,085 WARNING database DDL Query made to DB:
create table `tabNotification Subscribed Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,142 WARNING database DDL Query made to DB:
create table `tabKanban Board Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`column_name` varchar(140),
`status` varchar(140) default 'Active',
`indicator` varchar(140) default 'Gray',
`order` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,221 WARNING database DDL Query made to DB:
create table `tabToDo` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Open',
`priority` varchar(140) default 'Medium',
`color` varchar(140),
`date` date,
`allocated_to` varchar(140),
`description` longtext,
`reference_type` varchar(140),
`reference_name` varchar(140),
`role` varchar(140),
`assigned_by` varchar(140),
`assigned_by_full_name` varchar(140),
`sender` varchar(140),
`assignment_rule` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,260 WARNING database DDL Query made to DB:
ALTER TABLE `tabToDo`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-07-31 13:13:45,309 WARNING database DDL Query made to DB:
create table `tabChangelog Feed` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`app_name` varchar(140),
`link` longtext,
`posting_timestamp` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_timestamp`(`posting_timestamp`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,363 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`y_field` varchar(140),
`color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,419 WARNING database DDL Query made to DB:
create table `tabEvent Participants` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,611 WARNING database DDL Query made to DB:
create table `tabTag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,667 WARNING database DDL Query made to DB:
create table `tabRoute History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`route` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 13:13:45,723 WARNING database DDL Query made to DB:
create table `tabNumber Card Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`card` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
