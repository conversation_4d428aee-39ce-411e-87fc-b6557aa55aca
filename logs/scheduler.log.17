2025-06-10 19:02:31,684 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,742 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,744 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,747 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,749 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,751 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,754 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,757 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,759 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,761 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,767 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,770 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,772 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,775 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,778 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,780 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,783 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 19:02:31,785 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,100 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,103 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,154 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,157 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,159 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,162 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,165 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,169 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,172 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,175 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,178 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,185 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,189 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,192 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,195 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,198 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,201 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,204 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 19:03:32,207 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:33,974 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:33,977 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,028 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,031 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,034 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,037 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,039 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,042 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,044 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,048 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,051 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,058 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,060 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,063 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,066 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,068 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,072 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,076 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 19:04:34,078 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 19:09:38,706 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:09:38,778 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:09:38,782 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:13:42,507 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:13:42,568 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:13:42,571 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:14:43,962 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:14:44,079 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:14:44,085 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:15:45,477 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:15:45,537 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:15:45,539 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:25:55,584 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:25:55,643 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:25:55,645 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:30:00,295 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:30:00,365 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:30:00,368 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,503 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,557 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,559 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,563 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,565 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,567 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,569 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,572 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,574 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,576 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,578 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 19:31:00,580 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 19:33:03,018 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:33:03,076 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:33:03,078 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:33:03,084 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 19:34:03,975 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:34:04,033 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:34:04,036 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:34:04,042 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 19:35:04,324 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:35:04,401 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:35:04,405 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:35:04,414 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 19:36:05,802 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 19:36:05,862 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:36:05,865 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 19:36:05,872 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 20:41:19,114 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 20:41:19,190 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:41:19,193 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:41:19,201 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 20:42:20,697 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 20:42:20,833 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:42:20,837 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:42:20,849 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 20:45:24,262 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 20:45:24,335 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:45:24,338 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:25,858 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:25,974 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:25,978 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:25,993 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:25,999 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:26,004 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:26,008 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:26,012 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:26,017 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 20:46:26,022 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,574 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,670 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,673 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,682 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,686 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,688 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,691 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,694 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,697 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 20:47:27,700 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 20:51:32,390 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 20:52:33,369 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 20:54:36,279 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 20:55:37,799 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 20:56:38,156 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 20:57:40,024 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 20:57:40,106 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:57:40,109 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 20:57:40,117 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,616 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,622 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,681 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,684 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,687 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,690 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,693 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,696 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,699 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,702 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,705 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,712 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,715 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,718 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,722 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,725 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,728 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,733 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 21:01:44,736 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,628 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,631 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,705 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,711 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,714 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,719 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,721 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,724 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,727 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,731 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,736 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,743 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,747 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,750 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,752 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,755 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,758 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,762 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 21:02:46,767 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 21:05:49,836 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:05:49,981 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:05:49,987 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:06:51,894 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:06:51,966 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:06:51,969 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:07:52,641 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:07:52,749 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:07:52,755 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:46:40,475 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 21:46:40,478 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 21:46:40,481 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 21:46:40,483 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 21:46:40,486 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 21:46:40,488 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 21:46:40,490 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 21:47:41,406 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 21:47:41,409 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 21:47:41,412 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 21:47:41,414 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 21:47:41,419 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 21:47:41,422 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 21:47:41,425 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 21:48:41,813 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 21:48:41,815 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 21:48:41,818 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 21:48:41,820 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 21:48:41,822 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 21:48:41,825 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 21:48:41,827 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,131 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,225 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,229 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,241 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,244 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,248 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,251 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,254 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,257 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 21:49:43,260 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 21:51:45,871 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 21:52:46,999 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 21:53:47,622 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:53:47,705 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:53:47,709 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:53:47,717 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 21:54:49,100 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:54:49,171 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:54:49,174 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:54:49,182 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 21:57:52,633 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:57:52,714 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:57:52,717 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:58:53,813 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:58:53,877 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:58:53,880 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:59:54,208 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 21:59:54,292 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 21:59:54,295 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,607 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,662 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,665 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,667 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,670 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,674 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,676 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,678 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,687 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,689 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,691 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,694 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,696 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,698 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,700 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 22:01:57,702 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,229 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,285 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,287 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,290 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,292 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,295 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,297 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,300 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,310 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,313 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,315 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,318 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,320 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,323 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,326 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 22:02:58,329 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,857 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,922 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,925 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,929 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,931 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,934 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,937 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,940 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,951 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,953 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,956 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,959 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,962 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,964 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,967 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 22:03:59,970 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,321 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,395 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,399 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,404 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,408 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,411 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,415 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,419 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,436 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,439 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,442 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,445 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,448 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,452 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,456 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-10 22:05:00,459 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-10 22:33:34,157 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:33:34,222 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:33:34,225 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:34:34,678 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:34:34,756 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:34:34,759 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:35:36,204 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:35:36,265 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:35:36,268 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:37:37,965 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:37:38,026 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:37:38,029 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:38:39,228 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:38:39,300 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:38:39,304 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:39:39,803 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:39:39,881 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:39:39,884 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:40:40,505 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:40:40,658 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:40:40,661 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:45:46,087 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:45:46,172 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:45:46,175 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:49:50,089 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:49:50,153 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:49:50,156 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:50:50,276 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-10 22:50:50,335 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-10 22:50:50,338 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-12 18:04:08,961 ERROR scheduler Exception in Enqueue Events for Site nexus.com
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 806, in _write_bytes
    self._sock.sendall(data)
ConnectionResetError: [Errno 104] Connection reset by peer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 75, in enqueue_events_for_site
    if is_scheduler_inactive():
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 117, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 130, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 956, in _request_authentication
    self.write_packet(data)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 732, in write_packet
    self._write_bytes(data)
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/pymysql/connections.py", line 809, in _write_bytes
    raise err.OperationalError(
pymysql.err.OperationalError: (2006, "MySQL server has gone away (ConnectionResetError(104, 'Connection reset by peer'))")
2025-06-15 10:58:01,813 ERROR scheduler Skipped queueing helpdesk.helpdesk.doctype.hd_ticket.hd_ticket.close_tickets_after_n_days because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,817 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,819 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,828 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,831 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,834 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,836 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,839 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,842 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,844 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,847 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,849 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,852 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,855 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,857 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.set_auto_repeat_as_completed because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,860 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,862 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,865 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,868 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,870 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,873 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,875 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,878 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,881 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,883 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,886 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,889 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,891 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,894 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,896 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,899 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,901 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,904 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,907 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,909 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,912 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,915 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,917 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,920 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,923 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,925 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,928 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,931 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,933 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for brainwise.helpdesk
2025-06-15 10:58:01,949 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-06-15 10:58:01,952 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for nexus.com
2025-06-15 10:58:01,959 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for nexus.com
2025-06-15 10:58:01,961 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for nexus.com
2025-06-15 10:58:01,964 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for nexus.com
2025-06-15 10:58:01,966 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for nexus.com
2025-06-15 10:58:01,969 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for nexus.com
2025-06-15 10:58:02,047 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for nexus.com
2025-06-15 10:58:02,050 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for nexus.com
2025-06-15 10:58:02,053 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for nexus.com
2025-06-15 10:58:02,055 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for nexus.com
2025-06-15 10:58:02,058 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for nexus.com
2025-06-15 10:58:02,061 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for nexus.com
2025-06-15 10:58:02,063 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for nexus.com
2025-06-15 10:58:02,066 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for nexus.com
2025-06-15 10:58:02,068 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for nexus.com
2025-06-15 10:58:02,071 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for nexus.com
2025-06-15 10:58:02,074 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for nexus.com
2025-06-15 10:58:02,142 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for nexus.com
2025-06-15 10:58:02,156 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for theme.com
2025-06-15 10:58:02,160 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-06-15 10:58:02,163 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for theme.com
2025-06-15 10:58:02,172 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-06-15 10:58:02,175 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for theme.com
2025-06-15 10:58:02,178 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for theme.com
2025-06-15 10:58:02,180 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for theme.com
2025-06-15 10:58:02,183 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for theme.com
2025-06-15 10:58:02,186 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for theme.com
2025-06-15 10:58:02,189 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for theme.com
2025-06-15 10:58:02,192 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for theme.com
2025-06-15 10:58:02,195 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for theme.com
2025-06-15 10:58:02,198 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for theme.com
2025-06-15 10:58:02,201 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for theme.com
2025-06-15 10:58:02,203 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for theme.com
2025-06-15 10:58:02,206 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for theme.com
2025-06-15 10:58:02,209 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for theme.com
2025-06-15 10:58:02,212 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for theme.com
2025-06-15 10:58:02,214 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for theme.com
2025-06-15 10:58:02,217 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for theme.com
2025-06-15 10:58:02,220 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for theme.com
2025-06-15 10:58:02,223 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for theme.com
2025-06-15 10:58:02,226 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for theme.com
2025-06-15 10:58:02,229 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for theme.com
2025-06-15 10:58:02,231 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for theme.com
2025-06-15 10:58:02,234 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for theme.com
2025-06-15 10:58:02,237 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for theme.com
2025-06-15 10:58:02,239 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for theme.com
2025-06-15 10:58:02,242 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for theme.com
2025-06-15 10:58:02,244 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for theme.com
2025-06-15 10:58:02,247 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for theme.com
2025-06-15 10:58:02,250 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for theme.com
2025-06-15 10:58:02,252 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for theme.com
2025-06-15 10:58:02,255 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for theme.com
2025-06-15 10:58:02,258 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for theme.com
2025-06-15 10:58:02,260 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for theme.com
2025-06-15 10:58:02,263 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for theme.com
2025-06-15 10:58:02,266 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for theme.com
2025-06-15 10:58:02,268 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for theme.com
2025-06-15 10:58:02,277 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for theme.com
2025-06-15 10:58:02,280 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for theme.com
2025-06-15 10:58:02,283 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for theme.com
2025-06-15 10:58:02,286 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for theme.com
2025-06-15 10:58:02,288 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for theme.com
2025-06-15 10:58:02,291 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for theme.com
2025-06-15 10:58:02,294 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for theme.com
2025-06-15 10:58:02,297 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for theme.com
2025-06-15 10:58:02,299 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for theme.com
2025-06-15 10:58:02,302 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for theme.com
2025-06-15 10:58:02,306 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for theme.com
2025-06-15 10:58:02,309 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.set_auto_repeat_as_completed because it was found in queue for theme.com
2025-06-15 10:58:02,312 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for theme.com
2025-06-15 10:58:02,315 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for theme.com
2025-06-15 10:58:02,319 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for theme.com
2025-06-15 10:58:02,322 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for theme.com
2025-06-15 10:58:02,326 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for theme.com
2025-06-15 10:58:02,329 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for theme.com
2025-06-15 10:58:02,333 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for theme.com
2025-06-15 10:58:02,335 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for theme.com
2025-06-15 10:58:02,338 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for theme.com
2025-06-15 10:58:02,341 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for theme.com
2025-06-15 10:58:02,343 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for theme.com
2025-06-15 10:58:02,346 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for theme.com
2025-06-15 10:58:02,348 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for theme.com
2025-06-15 10:58:02,351 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for theme.com
2025-06-15 10:58:02,353 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for theme.com
2025-06-15 10:58:02,356 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for theme.com
2025-06-15 10:58:02,359 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for theme.com
2025-06-15 10:58:02,362 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for theme.com
2025-06-15 10:58:02,364 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for theme.com
2025-06-15 10:58:02,367 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for theme.com
2025-06-15 10:58:02,370 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for theme.com
2025-06-15 10:58:02,373 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for theme.com
2025-06-15 10:58:02,376 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for theme.com
2025-06-15 10:58:02,378 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for theme.com
2025-06-15 10:58:02,381 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for theme.com
2025-06-15 10:58:02,384 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for theme.com
2025-06-15 10:58:02,387 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for theme.com
2025-06-15 10:58:02,391 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for theme.com
2025-06-15 10:58:02,394 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for theme.com
2025-06-15 10:58:02,413 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-15 10:58:02,418 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for child_ngo
2025-06-15 10:58:02,422 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-06-15 10:58:02,438 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for child_ngo
2025-06-15 10:58:02,445 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-06-15 10:58:02,447 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-15 10:58:02,454 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-15 10:58:02,459 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-06-15 10:58:02,462 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-06-15 10:58:02,464 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-06-15 10:58:02,467 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-06-15 10:58:02,534 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for child_ngo
2025-06-15 10:58:02,537 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for child_ngo
2025-06-15 10:58:02,540 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for child_ngo
2025-06-15 10:58:02,542 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for child_ngo
2025-06-15 10:58:02,545 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for child_ngo
2025-06-15 10:58:02,547 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-06-15 10:58:02,550 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-06-15 10:58:02,552 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-15 10:58:02,555 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-06-15 10:58:02,622 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for customhr
2025-06-15 10:58:02,626 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for customhr
2025-06-15 10:58:02,629 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for customhr
2025-06-15 10:58:02,636 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for customhr
2025-06-15 10:58:02,638 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for customhr
2025-06-15 10:58:02,641 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for customhr
2025-06-15 10:58:02,644 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for customhr
2025-06-15 10:58:02,646 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for customhr
2025-06-15 10:58:02,649 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for customhr
2025-06-15 10:58:02,652 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for customhr
2025-06-15 10:58:02,655 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for customhr
2025-06-15 10:58:02,657 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for customhr
2025-06-15 10:58:02,660 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for customhr
2025-06-15 10:58:02,663 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for customhr
2025-06-15 10:58:02,666 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for customhr
2025-06-15 10:58:02,668 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for customhr
2025-06-15 10:58:02,671 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for customhr
2025-06-15 10:58:02,673 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for customhr
2025-06-15 10:58:02,676 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for customhr
2025-06-15 10:58:02,679 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for customhr
2025-06-15 10:58:02,681 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for customhr
2025-06-15 10:58:02,684 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for customhr
2025-06-15 10:58:02,687 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for customhr
2025-06-15 10:58:02,690 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for customhr
2025-06-15 10:58:02,693 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for customhr
2025-06-15 10:58:02,695 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for customhr
2025-06-15 10:58:02,699 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for customhr
2025-06-15 10:58:02,701 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for customhr
2025-06-15 10:58:02,704 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for customhr
2025-06-15 10:58:02,707 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for customhr
2025-06-15 10:58:02,709 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for customhr
2025-06-15 10:58:02,712 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for customhr
2025-06-15 10:58:02,714 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for customhr
2025-06-15 10:58:02,717 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for customhr
2025-06-15 10:58:02,719 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for customhr
2025-06-15 10:58:02,722 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for customhr
2025-06-15 10:58:02,725 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for customhr
2025-06-15 10:58:02,727 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for customhr
2025-06-15 10:58:02,731 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for customhr
2025-06-15 10:58:02,740 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for customhr
2025-06-15 10:58:02,743 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for customhr
2025-06-15 10:58:02,746 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for customhr
2025-06-15 10:58:02,748 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for customhr
2025-06-15 10:58:02,751 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for customhr
2025-06-15 10:58:02,754 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for customhr
2025-06-15 10:58:02,757 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for customhr
2025-06-15 10:58:02,759 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for customhr
2025-06-15 10:58:02,762 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for customhr
2025-06-15 10:58:02,765 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for customhr
2025-06-15 10:58:02,768 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for customhr
2025-06-15 10:58:02,771 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.set_auto_repeat_as_completed because it was found in queue for customhr
2025-06-15 10:58:02,774 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for customhr
2025-06-15 10:58:02,777 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for customhr
2025-06-15 10:58:02,779 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for customhr
2025-06-15 10:58:02,782 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for customhr
2025-06-15 10:58:02,784 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for customhr
2025-06-15 10:58:02,787 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for customhr
2025-06-15 10:58:02,790 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for customhr
2025-06-15 10:58:02,792 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for customhr
2025-06-15 10:58:02,795 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for customhr
2025-06-15 10:58:02,798 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for customhr
2025-06-15 10:58:02,800 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for customhr
2025-06-15 10:58:02,803 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for customhr
2025-06-15 10:58:02,805 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for customhr
2025-06-15 10:58:02,808 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for customhr
2025-06-15 10:58:02,811 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for customhr
2025-06-15 10:58:02,813 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for customhr
2025-06-15 10:58:02,816 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for customhr
2025-06-15 10:58:02,818 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for customhr
2025-06-15 10:58:02,821 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for customhr
2025-06-15 10:58:02,823 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for customhr
2025-06-15 10:58:02,826 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for customhr
2025-06-15 10:58:02,829 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for customhr
2025-06-15 10:58:02,832 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for customhr
2025-06-15 10:58:02,835 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for customhr
2025-06-15 10:58:02,839 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for customhr
2025-06-15 10:58:02,841 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for customhr
2025-06-15 10:58:02,844 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for customhr
2025-06-15 10:58:02,847 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for customhr
2025-06-15 10:58:02,850 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for customhr
2025-06-15 10:58:02,868 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-06-15 10:58:02,881 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for parentngo
2025-06-15 10:58:02,883 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for parentngo
2025-06-15 10:58:02,888 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-06-15 10:58:02,892 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-06-15 10:58:02,896 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-06-15 10:58:02,900 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-06-15 10:58:02,903 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-06-15 10:58:02,906 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-06-15 10:58:02,908 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-06-15 10:58:02,912 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-06-15 10:58:02,980 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for parentngo
2025-06-15 10:58:02,982 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for parentngo
2025-06-15 10:58:02,985 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for parentngo
2025-06-15 10:58:02,987 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for parentngo
2025-06-15 10:58:02,990 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for parentngo
2025-06-15 10:58:02,992 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-06-15 10:58:02,995 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-06-15 10:58:02,998 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-06-15 10:58:03,000 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-06-15 10:58:03,017 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-06-15 10:58:03,020 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for parentngo
2025-06-15 10:58:03,023 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-06-15 10:58:03,026 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-06-15 10:58:03,029 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for parentngo
2025-06-15 10:58:03,031 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for parentngo
2025-06-15 10:58:03,035 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-06-15 10:58:03,037 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
