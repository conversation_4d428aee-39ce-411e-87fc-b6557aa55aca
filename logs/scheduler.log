2025-07-20 11:56:10,313 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-07-20 11:56:10,316 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-07-20 11:56:10,321 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-07-20 11:56:10,325 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-20 11:56:10,330 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-07-20 11:56:10,334 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for child_ngo
2025-07-20 11:56:10,344 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for child_ngo
2025-07-20 11:56:10,348 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-07-20 11:56:10,353 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for child_ngo
2025-07-20 11:56:10,358 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-07-20 11:56:10,362 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-07-20 11:56:10,367 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-07-20 11:56:10,372 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-07-20 11:56:10,386 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-07-20 11:56:10,393 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-07-20 11:56:10,396 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-07-20 11:56:10,400 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-07-20 11:56:10,405 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-07-20 11:56:10,407 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-07-20 11:56:10,409 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-07-20 11:56:10,411 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for child_ngo
2025-07-20 11:56:10,414 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for child_ngo
2025-07-20 11:56:10,430 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-07-22 13:50:22,145 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for nexus.com
2025-07-22 13:50:22,154 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-07-22 13:50:22,173 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for nexus.com
2025-07-22 13:50:22,194 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for nexus.com
2025-07-22 13:50:22,203 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for nexus.com
2025-07-22 13:50:22,206 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for nexus.com
2025-07-22 13:50:22,387 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,391 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,399 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,405 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,409 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,412 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,415 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,418 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,423 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,430 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,440 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,444 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,446 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,452 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,460 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,462 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,467 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,473 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,475 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,481 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for brainwise.helpdesk
2025-07-22 13:50:22,932 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for theme.com
2025-07-22 13:50:22,943 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for theme.com
2025-07-22 13:50:22,950 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for theme.com
2025-07-22 13:50:22,977 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for theme.com
2025-07-22 13:50:23,003 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for theme.com
2025-07-22 13:50:23,020 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-07-22 13:50:23,024 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for theme.com
2025-07-22 13:50:23,027 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for theme.com
2025-07-22 13:50:23,034 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for theme.com
2025-07-22 13:50:23,043 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-07-22 13:50:23,047 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for theme.com
2025-07-22 13:50:23,057 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for theme.com
2025-07-22 13:50:23,089 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-07-22 13:50:23,093 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-07-22 13:50:23,095 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-07-22 13:50:23,106 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-07-22 13:50:23,109 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-07-22 13:50:23,118 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-07-22 13:50:23,121 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-07-22 13:50:23,122 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-07-22 13:50:23,124 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-07-22 13:50:23,127 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-07-22 13:50:23,132 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-07-22 13:50:23,141 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-07-22 13:50:23,146 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-07-22 13:50:23,148 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-07-22 13:50:23,150 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-07-22 13:50:23,162 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-07-22 13:50:23,167 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-07-22 13:50:23,170 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-07-22 13:50:23,178 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-07-22 13:50:23,185 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-07-22 13:50:23,192 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-07-22 13:50:23,199 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-07-22 13:50:23,206 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-07-22 13:50:23,211 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-07-22 13:50:23,222 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-07-22 13:50:23,227 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-07-22 13:50:23,230 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-07-22 13:50:23,233 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-07-27 11:29:07,504 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for testmahmoud
2025-07-27 11:29:07,508 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-07-27 11:29:07,513 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-07-27 11:29:07,515 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-07-27 11:29:07,518 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for testmahmoud
2025-07-27 11:29:07,522 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-07-27 11:29:07,524 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-07-27 11:29:07,526 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-07-27 11:29:07,531 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-07-27 11:29:07,536 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-07-27 11:29:07,545 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for testmahmoud
2025-07-27 11:29:07,549 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-07-27 11:29:07,555 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-07-27 11:29:07,556 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for testmahmoud
2025-07-27 11:29:07,558 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-07-27 11:29:07,564 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for testmahmoud
2025-07-27 11:29:07,571 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-07-27 11:29:07,580 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-07-27 11:29:07,585 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-07-27 11:29:07,591 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-07-27 11:29:07,594 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-07-27 11:29:07,597 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-27 11:29:07,599 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-07-27 11:29:07,600 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-07-27 11:29:07,602 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-07-27 11:29:07,608 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-07-27 11:29:07,610 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-27 11:29:07,611 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-07-27 11:29:07,614 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-07-27 11:29:07,615 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for testmahmoud
2025-07-27 11:29:07,617 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for testmahmoud
2025-07-27 11:29:07,618 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-07-27 11:29:07,620 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-07-27 11:29:07,622 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-07-27 11:29:07,634 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for serviceplanner
2025-07-27 11:29:07,639 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-07-27 11:29:07,642 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-27 11:29:07,650 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-27 11:29:07,661 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-27 11:29:07,663 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-27 11:29:07,671 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-27 11:29:07,674 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-27 11:29:07,676 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-27 11:29:07,700 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-27 11:29:07,702 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-07-27 11:29:07,707 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-27 11:29:07,710 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-27 11:29:07,712 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for serviceplanner
2025-07-27 11:29:07,722 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-28 14:35:19,278 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-07-28 14:35:19,281 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-07-28 14:35:19,289 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-07-28 14:35:19,293 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-07-28 14:35:19,301 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-28 14:35:19,304 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-07-28 14:35:19,307 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-07-28 14:35:19,310 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-07-28 14:35:19,317 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-07-28 14:35:19,322 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-07-28 14:35:19,328 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-07-28 14:35:19,331 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-07-28 14:35:19,333 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-07-28 14:35:19,346 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-07-28 14:35:19,353 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-07-28 14:35:19,365 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-07-28 14:35:19,374 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-07-28 14:35:19,381 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-07-28 14:35:19,383 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-07-28 14:35:19,388 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-07-28 14:35:19,393 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-07-28 14:35:19,398 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-07-28 14:35:19,407 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-07-28 14:35:19,411 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-07-28 14:35:19,423 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-07-28 14:35:19,426 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-07-28 14:35:19,435 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-07-28 14:35:19,455 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,462 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,466 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,471 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,474 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,480 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,490 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,493 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,499 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,505 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,508 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,514 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,524 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,536 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,543 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,553 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,559 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,565 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,568 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,571 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-07-28 14:35:19,610 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-07-28 14:35:19,620 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-28 14:35:19,632 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-07-28 14:35:19,639 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-07-28 14:35:19,651 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-07-28 14:35:19,657 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-07-28 14:35:19,664 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-07-28 14:35:19,667 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-07-28 14:35:19,680 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-07-28 14:35:19,686 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-28 14:35:19,694 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-07-28 14:35:19,698 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-07-28 14:35:19,715 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-07-28 14:35:19,721 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-07-28 14:35:19,750 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-07-28 14:35:19,754 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-07-28 14:35:19,765 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-07-28 14:35:19,768 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-07-28 14:35:19,789 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-07-28 14:35:19,805 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-07-28 14:35:19,814 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-07-28 14:35:19,821 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-07-28 14:35:19,825 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-07-28 14:35:19,837 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-07-28 14:35:19,843 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-07-28 14:35:19,852 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-07-28 14:35:19,863 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-07-28 14:35:19,880 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-07-28 14:35:19,958 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for customhr
2025-07-28 14:35:19,964 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for customhr
2025-07-28 14:35:19,970 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for customhr
2025-07-28 14:35:19,998 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for customhr
2025-07-28 14:35:20,013 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for customhr
2025-07-28 14:35:20,032 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for customhr
2025-07-28 14:35:20,041 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for customhr
2025-07-28 14:35:20,076 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for customhr
2025-07-28 14:35:20,088 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for customhr
2025-07-28 14:35:20,103 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for customhr
2025-07-28 14:35:20,106 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for customhr
2025-07-28 14:35:20,109 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for customhr
2025-07-28 14:35:20,133 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-07-28 14:35:20,139 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-07-28 14:35:20,159 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-07-28 14:35:20,172 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-07-28 14:35:20,178 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-07-28 14:35:20,182 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-07-28 14:35:20,185 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-07-28 14:35:20,188 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-07-28 14:35:20,191 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-07-28 14:35:20,196 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-07-28 14:35:20,199 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-07-28 14:35:20,207 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-07-28 14:35:20,226 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-07-28 14:35:20,234 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-28 14:35:20,241 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-07-28 14:35:20,254 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-07-28 14:35:20,272 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-07-28 14:35:20,275 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-07-28 14:35:20,279 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-28 14:35:20,285 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-07-28 14:35:20,290 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-07-28 14:35:20,297 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-07-28 14:35:20,300 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-07-28 14:35:20,312 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-07-28 14:35:20,328 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-07-28 14:35:20,336 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-07-28 14:35:20,344 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-07-28 14:35:20,364 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-07-28 14:35:20,367 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-07-28 14:35:20,372 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-07-28 14:35:20,380 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-07-28 14:35:20,387 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-07-28 14:35:20,400 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-07-28 14:35:20,404 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-07-28 14:35:20,406 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-07-28 14:35:20,408 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-07-28 14:35:20,411 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-07-28 14:35:20,420 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-07-28 14:35:20,422 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-07-28 14:35:20,424 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-07-28 14:35:20,426 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-07-28 14:35:20,436 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-07-28 14:35:20,438 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-07-28 14:35:20,440 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-07-28 14:35:20,445 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-07-28 14:35:20,449 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-07-28 14:35:20,456 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-07-28 14:35:20,464 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-07-28 14:35:20,471 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-07-28 14:35:20,480 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-07-28 14:35:20,505 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-07-28 14:35:20,510 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-07-28 14:35:20,513 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-07-28 14:35:20,516 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-07-28 14:35:20,523 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-07-28 14:35:20,542 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for theme.com
2025-07-28 14:35:20,549 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for theme.com
2025-07-28 14:35:20,553 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for theme.com
2025-07-28 14:35:20,571 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for theme.com
2025-07-28 14:35:20,582 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for theme.com
2025-07-28 14:35:20,589 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-07-28 14:35:20,607 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-07-28 14:35:20,618 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for theme.com
2025-07-28 14:35:20,636 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for theme.com
2025-07-28 14:35:20,661 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for theme.com
2025-07-28 14:35:20,670 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for theme.com
2025-07-28 14:35:20,709 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for theme.com
2025-07-28 14:35:20,758 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for nexus.com
2025-07-28 14:35:20,785 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for nexus.com
2025-07-28 14:35:20,791 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for nexus.com
2025-07-28 14:35:20,796 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for nexus.com
2025-07-28 14:35:20,804 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for nexus.com
2025-07-28 14:35:20,807 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for nexus.com
2025-07-28 14:35:20,814 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for nexus.com
2025-07-28 14:35:20,826 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for nexus.com
2025-07-28 14:35:20,831 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for nexus.com
2025-07-28 14:35:20,841 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-07-28 14:35:20,853 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for nexus.com
2025-07-28 14:35:20,870 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for nexus.com
2025-07-28 14:35:20,877 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for nexus.com
2025-07-28 14:35:20,880 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for nexus.com
2025-07-28 14:35:20,883 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for nexus.com
2025-07-28 14:35:20,889 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for nexus.com
2025-07-28 14:35:20,892 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for nexus.com
2025-07-28 14:35:20,899 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for nexus.com
2025-07-28 14:35:20,906 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for nexus.com
2025-07-28 14:35:20,910 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for nexus.com
2025-07-28 14:35:20,915 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for nexus.com
2025-07-28 14:35:20,925 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for nexus.com
2025-07-28 14:35:20,928 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for nexus.com
2025-07-28 14:35:20,939 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for nexus.com
2025-07-28 14:35:20,946 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for nexus.com
2025-07-28 14:35:20,950 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for nexus.com
2025-07-28 14:35:20,967 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for nexus.com
2025-07-28 14:36:21,677 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-07-28 14:36:21,689 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-07-28 14:36:21,696 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-07-28 14:36:21,705 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-07-28 14:36:21,715 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-07-28 14:36:21,733 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-07-28 14:36:21,741 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-07-28 14:36:21,744 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-07-28 14:36:21,746 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-07-28 14:36:21,752 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-07-28 14:36:21,756 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-07-28 14:36:21,763 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-07-28 14:36:21,771 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-07-28 14:36:21,778 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-07-28 14:36:21,800 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-07-28 14:36:21,813 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-07-28 14:36:21,819 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-07-28 14:36:21,822 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-07-28 14:36:21,832 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-07-28 14:36:21,837 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-07-28 14:36:21,845 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-07-28 14:36:21,859 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-07-28 14:36:21,872 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-07-28 14:36:21,880 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-07-28 14:36:21,889 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-07-28 14:36:21,901 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-07-28 14:36:21,920 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-07-28 14:36:21,926 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-07-28 14:36:22,055 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-07-28 14:36:23,081 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-07-28 14:36:23,094 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for nexus.com
2025-07-28 14:36:23,098 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for nexus.com
2025-07-28 14:36:23,104 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for nexus.com
2025-07-28 14:36:23,107 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for nexus.com
2025-07-28 14:36:23,116 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for nexus.com
2025-07-28 14:36:23,128 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for nexus.com
2025-07-28 14:36:23,131 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for nexus.com
2025-07-28 14:36:23,137 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for nexus.com
2025-07-28 14:36:23,140 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for nexus.com
2025-07-28 14:36:23,147 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for nexus.com
2025-07-28 14:36:23,176 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for nexus.com
2025-07-28 14:36:23,187 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for nexus.com
2025-07-28 14:36:23,205 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for nexus.com
2025-07-28 14:36:23,210 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for nexus.com
2025-07-28 14:36:23,214 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for nexus.com
2025-07-28 14:36:23,228 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for nexus.com
2025-07-28 14:36:23,237 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for nexus.com
2025-07-28 14:36:23,244 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for nexus.com
2025-07-28 14:36:23,253 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for nexus.com
2025-07-28 14:36:23,255 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for nexus.com
2025-07-28 14:36:23,260 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for nexus.com
2025-07-28 14:36:23,266 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for nexus.com
2025-07-28 14:36:23,277 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for nexus.com
2025-07-28 14:36:23,282 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for nexus.com
2025-07-28 14:36:23,287 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for nexus.com
2025-07-28 14:36:23,306 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for nexus.com
2025-07-30 15:00:33,320 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,338 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,339 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,351 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,354 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,356 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,358 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,361 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,369 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,371 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,373 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,380 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,388 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,398 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-07-30 15:00:33,535 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for customhr
2025-07-30 15:00:33,541 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for customhr
2025-07-30 15:00:33,551 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for customhr
2025-07-30 15:00:33,561 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for customhr
2025-07-30 15:00:33,574 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for customhr
2025-07-30 15:00:33,594 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for customhr
2025-07-30 15:00:33,597 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for customhr
2025-07-30 15:00:33,603 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for customhr
2025-07-30 15:00:33,606 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for customhr
2025-07-30 15:00:33,613 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for customhr
2025-07-30 15:00:33,615 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for customhr
2025-07-30 15:00:33,620 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for customhr
2025-07-30 15:00:33,993 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-07-30 15:00:34,003 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-07-30 15:00:34,005 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-07-30 15:00:34,007 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-07-30 15:00:34,008 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-07-30 15:00:34,011 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-07-30 15:00:34,013 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-07-30 15:00:34,016 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-07-30 15:00:34,020 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-07-30 15:00:34,023 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-07-30 15:00:34,026 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-07-30 15:00:34,027 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-07-30 15:00:34,033 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-07-30 15:00:34,035 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-07-30 15:00:34,039 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-07-30 15:00:34,042 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-07-30 15:00:34,044 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-07-30 15:00:34,045 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-07-30 15:00:34,053 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-07-30 15:00:34,060 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-07-30 15:00:34,068 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-07-30 15:00:34,069 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-07-30 15:00:34,075 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-07-30 15:00:34,076 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-07-30 15:00:34,088 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-07-30 15:00:34,094 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-07-30 15:00:34,099 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-07-30 15:00:34,105 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-07-30 15:00:34,122 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for theme.com
2025-07-30 15:00:34,133 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for theme.com
2025-07-30 15:00:34,138 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-07-30 15:00:34,153 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for theme.com
2025-07-30 15:00:34,159 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for theme.com
2025-07-30 15:00:34,172 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-07-30 15:00:34,193 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for theme.com
2025-07-30 15:00:34,206 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for theme.com
2025-07-30 15:00:34,208 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for theme.com
2025-07-30 15:00:34,222 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for theme.com
2025-07-30 15:00:34,230 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for theme.com
2025-07-30 15:00:34,238 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for theme.com
2025-07-30 15:00:34,251 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-07-30 15:00:34,253 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-07-30 15:00:34,260 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-07-30 15:00:34,262 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-07-30 15:00:34,268 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-07-30 15:00:34,276 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-07-30 15:00:34,278 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-07-30 15:00:34,282 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-07-30 15:00:34,289 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-07-30 15:00:34,293 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-30 15:00:34,299 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-07-30 15:00:34,301 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-07-30 15:00:34,302 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-07-30 15:00:34,307 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-07-30 15:00:34,311 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-07-30 15:00:34,316 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-07-30 15:00:34,320 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-07-30 15:00:34,322 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-07-30 15:00:34,328 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-07-30 15:00:34,333 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-07-30 15:00:34,337 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-07-30 15:00:34,340 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-07-30 15:00:34,347 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-07-30 15:00:34,349 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-07-30 15:00:34,350 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-07-30 15:00:34,360 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-07-30 15:00:34,365 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
