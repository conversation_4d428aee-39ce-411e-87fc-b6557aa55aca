### Feature Tracker

An internal feature tracking tool built with Frappe, designed to help teams monitor the development progress of product features, assign responsibilities, and track deadlines and status updates — all in one place.

### Installation

You can install this app using the [bench](https://github.com/frappe/bench) CLI:

```bash
cd $PATH_TO_YOUR_BENCH
bench get-app $URL_OF_THIS_REPO --branch develop
bench install-app feature_tracker
```

### License

mit
