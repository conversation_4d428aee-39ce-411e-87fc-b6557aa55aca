{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-07-31 13:33:16.993660", "doctype": "DocType", "engine": "InnoDB", "field_order": ["section_break_vmhx", "title", "status", "amended_from", "naming_series", "column_break_kpza", "priority", "date", "section_break_djfd", "description"], "fields": [{"fieldname": "section_break_vmhx", "fieldtype": "Section Break"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Feature Request", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"default": "Opened", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Opened\nPending\nIn Progress\nApproved\nRejected\nClosed\nCanceled", "read_only": 1, "reqd": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "hidden": 1, "label": "Naming Series", "options": "FRQ-.YYYY.-.#", "read_only": 1, "set_only_once": 1}, {"fieldname": "column_break_kpza", "fieldtype": "Column Break"}, {"default": "Low", "fieldname": "priority", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Priority", "options": "High\nMedium\nLow", "reqd": 1}, {"default": "Today", "fieldname": "date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Date", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_djfd", "fieldtype": "Section Break"}, {"fieldname": "description", "fieldtype": "Long Text", "label": "Description", "reqd": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-07-31 20:39:55.073129", "modified_by": "Administrator", "module": "Feature Tracker", "name": "Feature Request", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1, "track_views": 1}