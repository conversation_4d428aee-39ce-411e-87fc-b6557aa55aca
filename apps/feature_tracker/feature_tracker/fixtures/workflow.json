[{"docstatus": 0, "doctype": "Workflow", "document_type": "Feature Request", "is_active": 1, "modified": "2025-07-31 23:52:10.951089", "name": "Feature Request Workflow", "override_status": 0, "send_email_alert": 0, "states": [{"allow_edit": "All", "avoid_status_override": 0, "doc_status": "0", "is_optional_state": 0, "message": null, "next_action_email_template": null, "parent": "Feature Request Workflow", "parentfield": "states", "parenttype": "Workflow", "send_email": 1, "state": "Opened", "update_field": "status", "update_value": "Opened", "workflow_builder_id": "1"}, {"allow_edit": "All", "avoid_status_override": 0, "doc_status": "0", "is_optional_state": 0, "message": null, "next_action_email_template": null, "parent": "Feature Request Workflow", "parentfield": "states", "parenttype": "Workflow", "send_email": 1, "state": "Pending", "update_field": "status", "update_value": "Pending", "workflow_builder_id": "2"}, {"allow_edit": "All", "avoid_status_override": 0, "doc_status": "0", "is_optional_state": 0, "message": null, "next_action_email_template": null, "parent": "Feature Request Workflow", "parentfield": "states", "parenttype": "Workflow", "send_email": 1, "state": "In Progress", "update_field": "status", "update_value": "In Progress", "workflow_builder_id": "3"}, {"allow_edit": "All", "avoid_status_override": 0, "doc_status": "0", "is_optional_state": 0, "message": null, "next_action_email_template": null, "parent": "Feature Request Workflow", "parentfield": "states", "parenttype": "Workflow", "send_email": 1, "state": "Rejected", "update_field": "status", "update_value": "Rejected", "workflow_builder_id": "5"}, {"allow_edit": "All", "avoid_status_override": 0, "doc_status": "0", "is_optional_state": 0, "message": null, "next_action_email_template": null, "parent": "Feature Request Workflow", "parentfield": "states", "parenttype": "Workflow", "send_email": 1, "state": "Approved", "update_field": "status", "update_value": "Approved", "workflow_builder_id": "4"}, {"allow_edit": "All", "avoid_status_override": 0, "doc_status": "1", "is_optional_state": 0, "message": null, "next_action_email_template": null, "parent": "Feature Request Workflow", "parentfield": "states", "parenttype": "Workflow", "send_email": 1, "state": "Closed", "update_field": "status", "update_value": "Closed", "workflow_builder_id": "6"}, {"allow_edit": "All", "avoid_status_override": 0, "doc_status": "2", "is_optional_state": 0, "message": null, "next_action_email_template": null, "parent": "Feature Request Workflow", "parentfield": "states", "parenttype": "Workflow", "send_email": 1, "state": "Canceled", "update_field": "status", "update_value": "Canceled", "workflow_builder_id": null}], "transitions": [{"action": "Submit for review", "allow_self_approval": 1, "allowed": "All", "condition": null, "next_state": "Pending", "parent": "Feature Request Workflow", "parentfield": "transitions", "parenttype": "Workflow", "send_email_to_creator": 0, "state": "Opened", "workflow_builder_id": "action-1"}, {"action": "Approve feature", "allow_self_approval": 1, "allowed": "All", "condition": null, "next_state": "Approved", "parent": "Feature Request Workflow", "parentfield": "transitions", "parenttype": "Workflow", "send_email_to_creator": 0, "state": "Pending", "workflow_builder_id": "action-2"}, {"action": "Start implementation", "allow_self_approval": 1, "allowed": "All", "condition": null, "next_state": "In Progress", "parent": "Feature Request Workflow", "parentfield": "transitions", "parenttype": "Workflow", "send_email_to_creator": 0, "state": "Approved", "workflow_builder_id": "action-3"}, {"action": "Complete feature", "allow_self_approval": 1, "allowed": "All", "condition": null, "next_state": "Closed", "parent": "Feature Request Workflow", "parentfield": "transitions", "parenttype": "Workflow", "send_email_to_creator": 0, "state": "In Progress", "workflow_builder_id": "action-4"}, {"action": "Close without work", "allow_self_approval": 1, "allowed": "All", "condition": null, "next_state": "Closed", "parent": "Feature Request Workflow", "parentfield": "transitions", "parenttype": "Workflow", "send_email_to_creator": 0, "state": "Approved", "workflow_builder_id": "action-5"}, {"action": "Reject feature", "allow_self_approval": 1, "allowed": "All", "condition": null, "next_state": "Rejected", "parent": "Feature Request Workflow", "parentfield": "transitions", "parenttype": "Workflow", "send_email_to_creator": 0, "state": "Pending", "workflow_builder_id": "action-6"}, {"action": "Final closure", "allow_self_approval": 1, "allowed": "All", "condition": null, "next_state": "Closed", "parent": "Feature Request Workflow", "parentfield": "transitions", "parenttype": "Workflow", "send_email_to_creator": 0, "state": "Rejected", "workflow_builder_id": "action-7"}, {"action": "Cancel", "allow_self_approval": 1, "allowed": "All", "condition": null, "next_state": "Canceled", "parent": "Feature Request Workflow", "parentfield": "transitions", "parenttype": "Workflow", "send_email_to_creator": 0, "state": "Closed", "workflow_builder_id": null}], "workflow_data": "[{\"type\":\"state\",\"dimensions\":{\"width\":105,\"height\":53},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":48.890625,\"y\":-11,\"width\":7,\"height\":7},{\"id\":\"right\",\"position\":\"right\",\"x\":108.796875,\"y\":23,\"width\":7,\"height\":7},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":48.890625,\"y\":57,\"width\":7,\"height\":7},{\"id\":\"left\",\"position\":\"left\",\"x\":-11,\"y\":23,\"width\":7,\"height\":7}]},\"computedPosition\":{\"x\":550,\"y\":100,\"z\":0},\"id\":\"1\",\"position\":{\"x\":550,\"y\":100}},{\"type\":\"state\",\"dimensions\":{\"width\":107,\"height\":53},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":49.75,\"y\":-11,\"width\":7,\"height\":7},{\"id\":\"right\",\"position\":\"right\",\"x\":110.515625,\"y\":23,\"width\":7,\"height\":7},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":49.75,\"y\":57,\"width\":7,\"height\":7},{\"id\":\"left\",\"position\":\"left\",\"x\":-11,\"y\":23,\"width\":7,\"height\":7}]},\"computedPosition\":{\"x\":1005,\"y\":109,\"z\":0},\"id\":\"2\",\"position\":{\"x\":1005,\"y\":109}},{\"type\":\"state\",\"dimensions\":{\"width\":127,\"height\":53},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":60.1875,\"y\":-11,\"width\":7,\"height\":7},{\"id\":\"right\",\"position\":\"right\",\"x\":131.375,\"y\":23,\"width\":7,\"height\":7},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":60.1875,\"y\":57,\"width\":7,\"height\":7},{\"id\":\"left\",\"position\":\"left\",\"x\":-11,\"y\":23,\"width\":7,\"height\":7}]},\"computedPosition\":{\"x\":1825,\"y\":-53.19999999999982,\"z\":0},\"id\":\"3\",\"position\":{\"x\":1825,\"y\":-53.19999999999982}},{\"type\":\"state\",\"dimensions\":{\"width\":117,\"height\":53},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":54.921875,\"y\":-11,\"width\":7,\"height\":7},{\"id\":\"right\",\"position\":\"right\",\"x\":120.859375,\"y\":23,\"width\":7,\"height\":7},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":54.921875,\"y\":57,\"width\":7,\"height\":7},{\"id\":\"left\",\"position\":\"left\",\"x\":-11,\"y\":23,\"width\":7,\"height\":7}]},\"computedPosition\":{\"x\":1371.6,\"y\":29,\"z\":0},\"id\":\"4\",\"position\":{\"x\":1371.6,\"y\":29}},{\"type\":\"state\",\"dimensions\":{\"width\":111,\"height\":53},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":51.78125,\"y\":-11,\"width\":7,\"height\":7},{\"id\":\"right\",\"position\":\"right\",\"x\":114.5625,\"y\":23,\"width\":7,\"height\":7},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":51.78125,\"y\":57,\"width\":7,\"height\":7},{\"id\":\"left\",\"position\":\"left\",\"x\":-11,\"y\":23,\"width\":7,\"height\":7}]},\"computedPosition\":{\"x\":2179,\"y\":267,\"z\":0},\"id\":\"5\",\"position\":{\"x\":2179,\"y\":267}},{\"type\":\"state\",\"dimensions\":{\"width\":99,\"height\":53},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":45.8125,\"y\":-11,\"width\":7,\"height\":7},{\"id\":\"right\",\"position\":\"right\",\"x\":102.640625,\"y\":23,\"width\":7,\"height\":7},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":45.8125,\"y\":57,\"width\":7,\"height\":7},{\"id\":\"left\",\"position\":\"left\",\"x\":-11,\"y\":23,\"width\":7,\"height\":7}]},\"computedPosition\":{\"x\":2390.2000000000003,\"y\":-20,\"z\":0},\"id\":\"6\",\"position\":{\"x\":2390.2000000000003,\"y\":-20}},{\"type\":\"action\",\"dimensions\":{\"width\":139,\"height\":33},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":66.6875,\"y\":-2,\"width\":6,\"height\":6},{\"id\":\"right\",\"position\":\"right\",\"x\":135.375,\"y\":13.5,\"width\":6,\"height\":6},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":66.6875,\"y\":29,\"width\":6,\"height\":6},{\"id\":\"left\",\"position\":\"left\",\"x\":-2,\"y\":13.5,\"width\":6,\"height\":6}]},\"computedPosition\":{\"x\":800,\"y\":120,\"z\":0},\"id\":\"action-1\",\"position\":{\"x\":800,\"y\":120},\"data\":{\"from_id\":\"1\",\"to_id\":\"2\"}},{\"type\":\"action\",\"dimensions\":{\"width\":130,\"height\":33},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":61.921875,\"y\":-2,\"width\":6,\"height\":6},{\"id\":\"right\",\"position\":\"right\",\"x\":125.84375,\"y\":13.5,\"width\":6,\"height\":6},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":61.921875,\"y\":29,\"width\":6,\"height\":6},{\"id\":\"left\",\"position\":\"left\",\"x\":-2,\"y\":13.5,\"width\":6,\"height\":6}]},\"computedPosition\":{\"x\":1190,\"y\":39,\"z\":0},\"id\":\"action-2\",\"position\":{\"x\":1190,\"y\":39},\"data\":{\"from_id\":\"2\",\"to_id\":\"4\"}},{\"type\":\"action\",\"dimensions\":{\"width\":162,\"height\":33},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":78.0625,\"y\":-2,\"width\":6,\"height\":6},{\"id\":\"right\",\"position\":\"right\",\"x\":158.140625,\"y\":13.5,\"width\":6,\"height\":6},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":78.0625,\"y\":29,\"width\":6,\"height\":6},{\"id\":\"left\",\"position\":\"left\",\"x\":-2,\"y\":13.5,\"width\":6,\"height\":6}]},\"computedPosition\":{\"x\":1600,\"y\":-119,\"z\":0},\"id\":\"action-3\",\"position\":{\"x\":1600,\"y\":-119},\"data\":{\"from_id\":\"4\",\"to_id\":\"3\"}},{\"type\":\"action\",\"dimensions\":{\"width\":138,\"height\":33},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":66.109375,\"y\":-2,\"width\":6,\"height\":6},{\"id\":\"right\",\"position\":\"right\",\"x\":134.234375,\"y\":13.5,\"width\":6,\"height\":6},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":66.109375,\"y\":29,\"width\":6,\"height\":6},{\"id\":\"left\",\"position\":\"left\",\"x\":-2,\"y\":13.5,\"width\":6,\"height\":6}]},\"computedPosition\":{\"x\":2124.8,\"y\":-108,\"z\":0},\"id\":\"action-4\",\"position\":{\"x\":2124.8,\"y\":-108},\"data\":{\"from_id\":\"3\",\"to_id\":\"6\"}},{\"type\":\"action\",\"dimensions\":{\"width\":151,\"height\":33},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":72.28125,\"y\":-2,\"width\":6,\"height\":6},{\"id\":\"right\",\"position\":\"right\",\"x\":146.5625,\"y\":13.5,\"width\":6,\"height\":6},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":72.28125,\"y\":29,\"width\":6,\"height\":6},{\"id\":\"left\",\"position\":\"left\",\"x\":-2,\"y\":13.5,\"width\":6,\"height\":6}]},\"computedPosition\":{\"x\":1968,\"y\":190,\"z\":0},\"id\":\"action-5\",\"position\":{\"x\":1968,\"y\":190},\"data\":{\"from_id\":\"4\",\"to_id\":\"6\"}},{\"type\":\"action\",\"dimensions\":{\"width\":116,\"height\":33},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":54.75,\"y\":-2,\"width\":6,\"height\":6},{\"id\":\"right\",\"position\":\"right\",\"x\":111.5,\"y\":13.5,\"width\":6,\"height\":6},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":54.75,\"y\":29,\"width\":6,\"height\":6},{\"id\":\"left\",\"position\":\"left\",\"x\":-2,\"y\":13.5,\"width\":6,\"height\":6}]},\"computedPosition\":{\"x\":1206,\"y\":277,\"z\":0},\"id\":\"action-6\",\"position\":{\"x\":1206,\"y\":277},\"data\":{\"from_id\":\"2\",\"to_id\":\"5\"}},{\"type\":\"action\",\"dimensions\":{\"width\":107,\"height\":33},\"handleBounds\":{\"source\":[{\"id\":\"top\",\"position\":\"top\",\"x\":50.3125,\"y\":-2,\"width\":6,\"height\":6},{\"id\":\"right\",\"position\":\"right\",\"x\":102.625,\"y\":13.5,\"width\":6,\"height\":6},{\"id\":\"bottom\",\"position\":\"bottom\",\"x\":50.3125,\"y\":29,\"width\":6,\"height\":6},{\"id\":\"left\",\"position\":\"left\",\"x\":-2,\"y\":13.5,\"width\":6,\"height\":6}]},\"computedPosition\":{\"x\":2400,\"y\":120,\"z\":0},\"id\":\"action-7\",\"position\":{\"x\":2400,\"y\":120},\"data\":{\"from_id\":\"5\",\"to_id\":\"6\"}},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"1\",\"target\":\"action-1\",\"updatable\":true,\"id\":\"edge-1-action-1\",\"animated\":true,\"sourceX\":665.796875,\"sourceY\":126.5,\"targetX\":798,\"targetY\":136.5},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"action-1\",\"target\":\"2\",\"updatable\":true,\"id\":\"edge-action-1-2\",\"animated\":true,\"sourceX\":941.375,\"sourceY\":136.5,\"targetX\":994,\"targetY\":135.5,\"markerEnd\":{\"type\":\"arrow\",\"width\":15,\"height\":15,\"strokeWidth\":1.5,\"color\":\"#687178\"}},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"2\",\"target\":\"action-2\",\"updatable\":true,\"id\":\"edge-2-action-2\",\"animated\":true,\"sourceX\":1122.515625,\"sourceY\":135.5,\"targetX\":1188,\"targetY\":55.5},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"action-2\",\"target\":\"4\",\"updatable\":true,\"id\":\"edge-action-2-4\",\"animated\":true,\"sourceX\":1321.84375,\"sourceY\":55.5,\"targetX\":1360.6,\"targetY\":55.5,\"markerEnd\":{\"type\":\"arrow\",\"width\":15,\"height\":15,\"strokeWidth\":1.5,\"color\":\"#687178\"}},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"4\",\"target\":\"action-3\",\"updatable\":true,\"id\":\"edge-4-action-3\",\"animated\":true,\"sourceX\":1499.459375,\"sourceY\":55.5,\"targetX\":1598,\"targetY\":-102.5},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"action-3\",\"target\":\"3\",\"updatable\":true,\"id\":\"edge-action-3-3\",\"animated\":true,\"sourceX\":1764.140625,\"sourceY\":-102.5,\"targetX\":1814,\"targetY\":-26.699999999999818,\"markerEnd\":{\"type\":\"arrow\",\"width\":15,\"height\":15,\"strokeWidth\":1.5,\"color\":\"#687178\"}},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"3\",\"target\":\"action-4\",\"updatable\":true,\"id\":\"edge-3-action-4\",\"animated\":true,\"sourceX\":1963.375,\"sourceY\":-26.699999999999818,\"targetX\":2122.8,\"targetY\":-91.5},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"action-4\",\"target\":\"6\",\"updatable\":true,\"id\":\"edge-action-4-6\",\"animated\":true,\"sourceX\":2265.034375,\"sourceY\":-91.5,\"targetX\":2379.2000000000003,\"targetY\":6.5,\"markerEnd\":{\"type\":\"arrow\",\"width\":15,\"height\":15,\"strokeWidth\":1.5,\"color\":\"#687178\"}},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"4\",\"target\":\"action-5\",\"updatable\":true,\"id\":\"edge-4-action-5\",\"animated\":true,\"sourceX\":1499.459375,\"sourceY\":55.5,\"targetX\":1966,\"targetY\":206.5},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"action-5\",\"target\":\"6\",\"updatable\":true,\"id\":\"edge-action-5-6\",\"animated\":true,\"sourceX\":2120.5625,\"sourceY\":206.5,\"targetX\":2379.2000000000003,\"targetY\":6.5,\"markerEnd\":{\"type\":\"arrow\",\"width\":15,\"height\":15,\"strokeWidth\":1.5,\"color\":\"#687178\"}},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"2\",\"target\":\"action-6\",\"updatable\":true,\"id\":\"edge-2-action-6\",\"animated\":true,\"sourceX\":1122.515625,\"sourceY\":135.5,\"targetX\":1204,\"targetY\":293.5},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"action-6\",\"target\":\"5\",\"updatable\":true,\"id\":\"edge-action-6-5\",\"animated\":true,\"sourceX\":1323.5,\"sourceY\":293.5,\"targetX\":2168,\"targetY\":293.5,\"markerEnd\":{\"type\":\"arrow\",\"width\":15,\"height\":15,\"strokeWidth\":1.5,\"color\":\"#687178\"}},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"5\",\"target\":\"action-7\",\"updatable\":true,\"id\":\"edge-5-action-7\",\"animated\":true,\"sourceX\":2300.5625,\"sourceY\":293.5,\"targetX\":2398,\"targetY\":136.5},{\"sourceHandle\":\"right\",\"targetHandle\":\"left\",\"type\":\"transition\",\"source\":\"action-7\",\"target\":\"6\",\"updatable\":true,\"id\":\"edge-action-7-6\",\"animated\":true,\"sourceX\":2508.625,\"sourceY\":136.5,\"targetX\":2379.2000000000003,\"targetY\":6.5,\"markerEnd\":{\"type\":\"arrow\",\"width\":15,\"height\":15,\"strokeWidth\":1.5,\"color\":\"#687178\"}}]", "workflow_name": "Feature Request Workflow", "workflow_state_field": "workflow_state"}]