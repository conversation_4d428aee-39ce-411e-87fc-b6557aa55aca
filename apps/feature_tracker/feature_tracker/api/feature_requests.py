"""
Feature Request CRUD API

This module provides comprehensive CRUD operations for Feature Request doctype
with proper authentication, validation, and error handling.

API Endpoints:
- GET /api/method/feature_tracker.api.feature_requests.get_feature_requests - List all feature requests
- GET /api/method/feature_tracker.api.feature_requests.get_feature_request - Get single feature request
- POST /api/method/feature_tracker.api.feature_requests.create_feature_request - Create new feature request
- PUT /api/method/feature_tracker.api.feature_requests.update_feature_request - Update existing feature request
- DELETE /api/method/feature_tracker.api.feature_requests.delete_feature_request - Delete feature request
"""

import frappe
from frappe import _
from frappe.utils import cint
from typing import Dict, Optional, Any
import json


@frappe.whitelist()
def get_feature_requests(
    filters: Optional[str] = None,
    fields: Optional[str] = None,
    order_by: Optional[str] = None,
    limit_start: int = 0,
    limit_page_length: int = 20,
    search: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get list of feature requests with filtering, pagination, and search capabilities.

    Args:
        filters (str, optional): JSON string of filters to apply
        fields (str, optional): Comma-separated list of fields to return
        order_by (str, optional): Field to order by (default: 'creation desc')
        limit_start (int): Starting index for pagination (default: 0)
        limit_page_length (int): Number of records per page (default: 20)
        search (str, optional): Search term to filter by title or description

    Returns:
        Dict containing:
        - data: List of feature requests
        - total_count: Total number of records
        - page_info: Pagination information

    Raises:
        frappe.PermissionError: If user doesn't have read permission
        frappe.ValidationError: If invalid parameters provided
    """
    try:
        # Check permissions
        if not frappe.has_permission("Feature Request", "read"):
            frappe.throw(_("Not permitted to read Feature Request"), frappe.PermissionError)

        # Parse filters
        parsed_filters = {}
        if filters:
            try:
                parsed_filters = json.loads(filters) if isinstance(filters, str) else filters
            except (json.JSONDecodeError, TypeError):
                frappe.throw(_("Invalid filters format"), frappe.ValidationError)

        # Parse fields
        if fields:
            field_list = [field.strip() for field in fields.split(',')]
        else:
            field_list = [
                'name', 'title', 'description', 'status', 'priority',
                'date', 'creation', 'modified', 'owner', 'modified_by'
            ]

        # Set default order
        if not order_by:
            order_by = 'creation desc'

        # Add search filters
        if search:
            search_term = f'%{search}%'
            if parsed_filters:
                # Combine existing filters with search using AND
                parsed_filters = {
                    **parsed_filters,
                    'title': ['like', search_term]
                }
            else:
                # Use OR condition for title and description search
                parsed_filters = [
                    ['title', 'like', search_term],
                    ['description', 'like', search_term]
                ]

        # Validate pagination parameters
        limit_start = cint(limit_start)
        limit_page_length = cint(limit_page_length)

        if limit_page_length > 100:
            limit_page_length = 100  # Maximum limit for performance

        # Get total count
        total_count = frappe.db.count('Feature Request', filters=parsed_filters)

        # Get data
        data = frappe.get_all(
            'Feature Request',
            filters=parsed_filters,
            fields=field_list,
            order_by=order_by,
            limit_start=limit_start,
            limit_page_length=limit_page_length
        )

        # Calculate pagination info
        total_pages = (total_count + limit_page_length - 1) // limit_page_length
        current_page = (limit_start // limit_page_length) + 1

        return {
            'data': data,
            'total_count': total_count,
            'page_info': {
                'current_page': current_page,
                'total_pages': total_pages,
                'limit_start': limit_start,
                'limit_page_length': limit_page_length,
                'has_next': current_page < total_pages,
                'has_previous': current_page > 1
            }
        }

    except frappe.PermissionError:
        raise
    except frappe.ValidationError:
        raise
    except Exception as e:
        frappe.log_error(f"Error in get_feature_requests: {str(e)}")
        frappe.throw(_("An error occurred while fetching feature requests"), frappe.ValidationError)


@frappe.whitelist()
def get_feature_request(name: str) -> Dict[str, Any]:
    """
    Get a single feature request by name.

    Args:
        name (str): Name/ID of the feature request

    Returns:
        Dict containing feature request data

    Raises:
        frappe.PermissionError: If user doesn't have read permission
        frappe.DoesNotExistError: If feature request doesn't exist
    """
    try:
        # Validate input
        if not name:
            frappe.throw(_("Feature Request name is required"), frappe.ValidationError)

        # Check if document exists
        if not frappe.db.exists("Feature Request", name):
            frappe.throw(_("Feature Request {0} does not exist").format(name), frappe.DoesNotExistError)

        # Check permissions
        if not frappe.has_permission("Feature Request", "read", name):
            frappe.throw(_("Not permitted to read Feature Request {0}").format(name), frappe.PermissionError)

        # Get document
        doc = frappe.get_doc("Feature Request", name)

        return {
            'name': doc.name,
            'title': doc.title,
            'description': doc.description,
            'status': doc.status,
            'priority': doc.priority,
            'date': doc.date,
            'creation': doc.creation,
            'modified': doc.modified,
            'owner': doc.owner,
            'modified_by': doc.modified_by,
            'docstatus': doc.docstatus
        }

    except (frappe.PermissionError, frappe.DoesNotExistError, frappe.ValidationError):
        raise
    except Exception as e:
        frappe.log_error(f"Error in get_feature_request: {str(e)}")
        frappe.throw(_("An error occurred while fetching the feature request"), frappe.ValidationError)


@frappe.whitelist()
def create_feature_request(
    title: str,
    description: str,
    priority: str = "Medium",
    status: str = "Opened",
    date: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new feature request.

    Args:
        title (str): Title of the feature request (required)
        description (str): Description of the feature request (required)
        priority (str): Priority level (High, Medium, Low) - default: Medium
        status (str): Status of the request - default: Opened
        date (str, optional): Date in YYYY-MM-DD format - default: today

    Returns:
        Dict containing created feature request data

    Raises:
        frappe.PermissionError: If user doesn't have create permission
        frappe.ValidationError: If validation fails
    """
    try:
        # Check permissions
        if not frappe.has_permission("Feature Request", "create"):
            frappe.throw(_("Not permitted to create Feature Request"), frappe.PermissionError)

        # Validate required fields
        if not title or not title.strip():
            frappe.throw(_("Title is required"), frappe.ValidationError)

        if not description or not description.strip():
            frappe.throw(_("Description is required"), frappe.ValidationError)

        # Validate priority
        valid_priorities = ["High", "Medium", "Low"]
        if priority not in valid_priorities:
            frappe.throw(_("Priority must be one of: {0}").format(", ".join(valid_priorities)), frappe.ValidationError)

        # Validate status
        valid_statuses = ["Opened", "Pending", "In Progress", "Approved", "Rejected", "Closed", "Canceled"]
        if status not in valid_statuses:
            frappe.throw(_("Status must be one of: {0}").format(", ".join(valid_statuses)), frappe.ValidationError)

        # Validate date
        if date:
            try:
                from frappe.utils import getdate
                getdate(date)
            except Exception:
                frappe.throw(_("Invalid date format. Use YYYY-MM-DD"), frappe.ValidationError)
        else:
            from frappe.utils import today
            date = today()

        # Create document
        doc = frappe.get_doc({
            "doctype": "Feature Request",
            "title": title.strip(),
            "description": description.strip(),
            "priority": priority,
            "status": status,
            "date": date
        })

        # Insert document
        doc.insert()

        # Commit transaction
        frappe.db.commit()

        return {
            'name': doc.name,
            'title': doc.title,
            'description': doc.description,
            'status': doc.status,
            'priority': doc.priority,
            'date': doc.date,
            'creation': doc.creation,
            'modified': doc.modified,
            'owner': doc.owner,
            'modified_by': doc.modified_by,
            'docstatus': doc.docstatus,
            'message': _("Feature Request created successfully")
        }

    except (frappe.PermissionError, frappe.ValidationError):
        raise
    except Exception as e:
        frappe.log_error(f"Error in create_feature_request: {str(e)}")
        frappe.throw(_("An error occurred while creating the feature request"), frappe.ValidationError)


@frappe.whitelist()
def update_feature_request(
    name: str,
    title: Optional[str] = None,
    description: Optional[str] = None,
    priority: Optional[str] = None,
    status: Optional[str] = None,
    date: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update an existing feature request.

    Args:
        name (str): Name/ID of the feature request to update (required)
        title (str, optional): New title
        description (str, optional): New description
        priority (str, optional): New priority (High, Medium, Low)
        status (str, optional): New status
        date (str, optional): New date in YYYY-MM-DD format

    Returns:
        Dict containing updated feature request data

    Raises:
        frappe.PermissionError: If user doesn't have write permission
        frappe.DoesNotExistError: If feature request doesn't exist
        frappe.ValidationError: If validation fails
    """
    try:
        # Validate input
        if not name:
            frappe.throw(_("Feature Request name is required"), frappe.ValidationError)

        # Check if document exists
        if not frappe.db.exists("Feature Request", name):
            frappe.throw(_("Feature Request {0} does not exist").format(name), frappe.DoesNotExistError)

        # Check permissions
        if not frappe.has_permission("Feature Request", "write", name):
            frappe.throw(_("Not permitted to update Feature Request {0}").format(name), frappe.PermissionError)

        # Get document
        doc = frappe.get_doc("Feature Request", name)

        # Update fields if provided
        if title is not None:
            if not title.strip():
                frappe.throw(_("Title cannot be empty"), frappe.ValidationError)
            doc.title = title.strip()

        if description is not None:
            if not description.strip():
                frappe.throw(_("Description cannot be empty"), frappe.ValidationError)
            doc.description = description.strip()

        if priority is not None:
            valid_priorities = ["High", "Medium", "Low"]
            if priority not in valid_priorities:
                frappe.throw(_("Priority must be one of: {0}").format(", ".join(valid_priorities)), frappe.ValidationError)
            doc.priority = priority

        if status is not None:
            valid_statuses = ["Opened", "Pending", "In Progress", "Approved", "Rejected", "Closed", "Canceled"]
            if status not in valid_statuses:
                frappe.throw(_("Status must be one of: {0}").format(", ".join(valid_statuses)), frappe.ValidationError)
            doc.status = status

        if date is not None:
            try:
                from frappe.utils import getdate
                getdate(date)
                doc.date = date
            except Exception:
                frappe.throw(_("Invalid date format. Use YYYY-MM-DD"), frappe.ValidationError)

        # Save document
        doc.save()

        # Commit transaction
        frappe.db.commit()

        return {
            'name': doc.name,
            'title': doc.title,
            'description': doc.description,
            'status': doc.status,
            'priority': doc.priority,
            'date': doc.date,
            'creation': doc.creation,
            'modified': doc.modified,
            'owner': doc.owner,
            'modified_by': doc.modified_by,
            'docstatus': doc.docstatus,
            'message': _("Feature Request updated successfully")
        }

    except (frappe.PermissionError, frappe.DoesNotExistError, frappe.ValidationError):
        raise
    except Exception as e:
        frappe.log_error(f"Error in update_feature_request: {str(e)}")
        frappe.throw(_("An error occurred while updating the feature request"), frappe.ValidationError)


@frappe.whitelist()
def delete_feature_request(name: str) -> Dict[str, Any]:
    """
    Delete a feature request.

    Args:
        name (str): Name/ID of the feature request to delete

    Returns:
        Dict containing success message

    Raises:
        frappe.PermissionError: If user doesn't have delete permission
        frappe.DoesNotExistError: If feature request doesn't exist
        frappe.ValidationError: If validation fails
    """
    try:
        # Validate input
        if not name:
            frappe.throw(_("Feature Request name is required"), frappe.ValidationError)

        # Check if document exists
        if not frappe.db.exists("Feature Request", name):
            frappe.throw(_("Feature Request {0} does not exist").format(name), frappe.DoesNotExistError)

        # Check permissions
        if not frappe.has_permission("Feature Request", "delete", name):
            frappe.throw(_("Not permitted to delete Feature Request {0}").format(name), frappe.PermissionError)

        # Get document for logging purposes
        doc = frappe.get_doc("Feature Request", name)
        title = doc.title

        # Delete document
        frappe.delete_doc("Feature Request", name)

        # Commit transaction
        frappe.db.commit()

        return {
            'name': name,
            'title': title,
            'message': _("Feature Request '{0}' deleted successfully").format(title)
        }

    except (frappe.PermissionError, frappe.DoesNotExistError, frappe.ValidationError):
        raise
    except Exception as e:
        frappe.log_error(f"Error in delete_feature_request: {str(e)}")
        frappe.throw(_("An error occurred while deleting the feature request"), frappe.ValidationError)


@frappe.whitelist()
def get_feature_request_stats() -> Dict[str, Any]:
    """
    Get statistics about feature requests.

    Returns:
        Dict containing various statistics

    Raises:
        frappe.PermissionError: If user doesn't have read permission
    """
    try:
        # Check permissions
        if not frappe.has_permission("Feature Request", "read"):
            frappe.throw(_("Not permitted to read Feature Request"), frappe.PermissionError)

        # Get status counts
        status_counts = frappe.db.sql("""
            SELECT status, COUNT(*) as count
            FROM `tabFeature Request`
            GROUP BY status
            ORDER BY count DESC
        """, as_dict=True)

        # Get priority counts
        priority_counts = frappe.db.sql("""
            SELECT priority, COUNT(*) as count
            FROM `tabFeature Request`
            GROUP BY priority
            ORDER BY FIELD(priority, 'High', 'Medium', 'Low')
        """, as_dict=True)

        # Get total count
        total_count = frappe.db.count("Feature Request")

        # Get recent activity (last 7 days)
        from frappe.utils import add_days, today
        recent_date = add_days(today(), -7)
        recent_count = frappe.db.count("Feature Request", {"creation": [">=", recent_date]})

        return {
            'total_count': total_count,
            'recent_count': recent_count,
            'status_breakdown': status_counts,
            'priority_breakdown': priority_counts
        }

    except frappe.PermissionError:
        raise
    except Exception as e:
        frappe.log_error(f"Error in get_feature_request_stats: {str(e)}")
        frappe.throw(_("An error occurred while fetching statistics"), frappe.ValidationError)


@frappe.whitelist()
def bulk_update_status(names: str, status: str) -> Dict[str, Any]:
    """
    Bulk update status for multiple feature requests.

    Args:
        names (str): JSON string of feature request names
        status (str): New status to set

    Returns:
        Dict containing update results

    Raises:
        frappe.PermissionError: If user doesn't have write permission
        frappe.ValidationError: If validation fails
    """
    try:
        # Parse names
        try:
            name_list = json.loads(names) if isinstance(names, str) else names
        except (json.JSONDecodeError, TypeError):
            frappe.throw(_("Invalid names format"), frappe.ValidationError)

        if not isinstance(name_list, list) or not name_list:
            frappe.throw(_("Names must be a non-empty list"), frappe.ValidationError)

        # Validate status
        valid_statuses = ["Opened", "Pending", "In Progress", "Approved", "Rejected", "Closed", "Canceled"]
        if status not in valid_statuses:
            frappe.throw(_("Status must be one of: {0}").format(", ".join(valid_statuses)), frappe.ValidationError)

        updated_count = 0
        failed_updates = []

        for name in name_list:
            try:
                # Check if document exists and user has permission
                if not frappe.db.exists("Feature Request", name):
                    failed_updates.append({"name": name, "error": "Document does not exist"})
                    continue

                if not frappe.has_permission("Feature Request", "write", name):
                    failed_updates.append({"name": name, "error": "No permission to update"})
                    continue

                # Update document
                doc = frappe.get_doc("Feature Request", name)
                doc.status = status
                doc.save()
                updated_count += 1

            except Exception as e:
                failed_updates.append({"name": name, "error": str(e)})

        # Commit transaction
        frappe.db.commit()

        return {
            'updated_count': updated_count,
            'failed_count': len(failed_updates),
            'failed_updates': failed_updates,
            'message': _("Bulk update completed. {0} records updated, {1} failed").format(updated_count, len(failed_updates))
        }

    except (frappe.PermissionError, frappe.ValidationError):
        raise
    except Exception as e:
        frappe.log_error(f"Error in bulk_update_status: {str(e)}")
        frappe.throw(_("An error occurred during bulk update"), frappe.ValidationError)